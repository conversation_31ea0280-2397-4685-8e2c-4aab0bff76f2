# 🚀 Plano de Implementação - Sistema de Snapshot DataHero4 MVP

## 📋 Contexto Atual

Com base na análise do repositório DataHero4 (branch `dashboard`), o sistema já possui:

✅ **Backend Estruturado**:
- `KpiDefinition` model em PostgreSQL com 34 KPIs migrados
- `KpiCalculationService` com queries reais implementadas para 8 KPIs
- Cache hierárquico funcionando (memória + Redis + PostgreSQL)
- Conexão com banco L2M funcionando

✅ **Frontend Moderno**:
- Dashboard com shadcn/ui e Recharts
- Hook `useKpiData` conectado à API real
- Sistema de cache local implementado

❌ **Problemas Identificados**:
- Performance ruim (5-60 segundos por KPI)
- Banco read-only sem índices
- Dados históricos desatualizados
- 34 KPIs é complexo demais para MVP

---

## 🎯 Objetivo: Sistema de Snapshot Dinâmico

Criar um sistema que:
1. **Calcula os 6 KPIs críticos** uma vez por dia
2. **Salva em snapshot** para acesso instantâneo  
3. **Usa definições existentes** do banco (sem hardcode)
4. **Mantém compatibilidade** com sistema atual

---

## 📁 Estrutura de Implementação

### 1️⃣ **Configuração dos KPIs Críticos**

**Arquivo**: `apps/backend/src/config/critical_kpis.py`

```python
"""
Critical KPIs Configuration for MVP
===================================

Define os 6 KPIs críticos para operadoras de câmbio baseado em 
melhores práticas do setor.
"""

from typing import List, Dict, Any
from enum import Enum

class KpiPriority(str, Enum):
    CRITICAL = "critical"  # 6 KPIs do MVP
    HIGH = "high"         # Próxima fase
    MEDIUM = "medium"     # Futura expansão
    LOW = "low"          # Nice to have

class CriticalKpisConfig:
    """
    Define os 6 KPIs críticos baseados em pesquisa de mercado:
    
    1. Transaction Volume - Volume total negociado
    2. FX Spread Margin - Margem de spread cambial
    3. Conversion Rate - Taxa de conversão de cotações
    4. Average Transaction Value - Ticket médio
    5. Active Clients - Clientes ativos no período
    6. Daily Transaction Count - Operações diárias
    """
    
    # IDs dos KPIs críticos (devem corresponder aos IDs no banco)
    CRITICAL_KPI_IDS = [
        "volume-total-transacoes",      # Total transaction volume
        "spread-medio",                 # Average FX spread
        "taxa-conversao",              # Conversion rate
        "ticket-medio",                # Average transaction value
        "numero-clientes-ativos",      # Active clients count
        "numero-operacoes"             # Daily operations count
    ]
    
    @classmethod
    def get_critical_kpi_ids(cls) -> List[str]:
        """Retorna lista de IDs dos KPIs críticos."""
        return cls.CRITICAL_KPI_IDS
    
    @classmethod
    def is_critical_kpi(cls, kpi_id: str) -> bool:
        """Verifica se um KPI é crítico."""
        return kpi_id in cls.CRITICAL_KPI_IDS
```

---

### 2️⃣ **Serviço de Geração de Snapshot**

**Arquivo**: `apps/backend/src/services/snapshot_service.py`

```python
"""
Snapshot Service for DataHero4 MVP
==================================

Gera snapshots diários dos KPIs críticos para performance otimizada.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from src.config.critical_kpis import CriticalKpisConfig
from src.services.kpi_service import get_kpi_service
from src.models.learning_models import KpiDefinition
from src.utils.learning_db_utils import get_db_manager

logger = logging.getLogger(__name__)


class SnapshotService:
    """Serviço para geração e gestão de snapshots de KPIs."""
    
    def __init__(self):
        self.kpi_service = get_kpi_service()
        self.db_manager = get_db_manager()
        self.snapshot_dir = Path("data/snapshots")
        self.snapshot_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_daily_snapshot(self, client_id: str = "L2M") -> Dict[str, Any]:
        """
        Gera snapshot diário com os 6 KPIs críticos.
        
        Args:
            client_id: Identificador do cliente
            
        Returns:
            Dict com snapshot gerado
        """
        logger.info(f"🔄 Iniciando geração de snapshot para cliente {client_id}")
        
        try:
            # 1. Buscar definições dos KPIs críticos no banco
            critical_kpis = self._get_critical_kpi_definitions()
            
            if not critical_kpis:
                logger.error("❌ Nenhum KPI crítico encontrado no banco!")
                return self._generate_error_snapshot("No critical KPIs found")
            
            logger.info(f"📊 Encontrados {len(critical_kpis)} KPIs críticos")
            
            # 2. Calcular valor de cada KPI usando o serviço existente
            kpi_data = {}
            for kpi_def in critical_kpis:
                logger.info(f"🧮 Calculando KPI: {kpi_def['id']}")
                
                # Usar método existente do KpiCalculationService
                kpi_result = self.kpi_service.calculate_kpi_value_from_dict(
                    kpi_dict=kpi_def,
                    client_id=client_id
                )
                
                if kpi_result:
                    # Formatar para snapshot
                    kpi_data[kpi_def['id']] = {
                        'value': kpi_result['currentValue'],
                        'formatted': self._format_kpi_value(
                            kpi_result['currentValue'],
                            kpi_def['format_type']
                        ),
                        'title': kpi_def['name'],
                        'description': kpi_def['description'],
                        'icon': self._get_kpi_icon(kpi_def['id']),
                        'format': kpi_def['format_type'],
                        'unit': kpi_def.get('unit', ''),
                        'category': kpi_def['category']
                    }
                else:
                    logger.warning(f"⚠️ Falha ao calcular KPI: {kpi_def['id']}")
            
            # 3. Criar estrutura do snapshot
            snapshot = {
                "metadata": {
                    "date": datetime.now().strftime("%Y-%m-%d"),
                    "time": datetime.now().strftime("%H:%M:%S"),
                    "client_id": client_id,
                    "version": "1.0",
                    "kpi_count": len(kpi_data)
                },
                "kpis": kpi_data,
                "summary": {
                    "total_calculated": len(kpi_data),
                    "total_expected": len(critical_kpis),
                    "success_rate": (len(kpi_data) / len(critical_kpis) * 100) if critical_kpis else 0
                }
            }
            
            # 4. Salvar snapshot
            self._save_snapshot(snapshot)
            
            logger.info(f"✅ Snapshot gerado com sucesso: {len(kpi_data)} KPIs")
            return snapshot
            
        except Exception as e:
            logger.error(f"❌ Erro ao gerar snapshot: {e}")
            return self._generate_error_snapshot(str(e))
    
    def _get_critical_kpi_definitions(self) -> List[Dict[str, Any]]:
        """Busca definições dos KPIs críticos no banco."""
        try:
            with self.db_manager.get_session() as session:
                # Buscar apenas KPIs críticos
                critical_ids = CriticalKpisConfig.get_critical_kpi_ids()
                
                kpis = session.query(KpiDefinition).filter(
                    KpiDefinition.id.in_(critical_ids),
                    KpiDefinition.is_active == True
                ).order_by(KpiDefinition.display_order).all()
                
                # Converter para dict dentro da sessão
                return [kpi.to_dict() for kpi in kpis]
                
        except Exception as e:
            logger.error(f"Erro ao buscar KPIs críticos: {e}")
            return []
    
    def _format_kpi_value(self, value: float, format_type: str) -> str:
        """Formata valor do KPI para exibição."""
        if value is None:
            return "N/A"
            
        if format_type == "currency":
            # Formatar como moeda brasileira
            if value >= 1e9:
                return f"R$ {value/1e9:.1f}B"
            elif value >= 1e6:
                return f"R$ {value/1e6:.1f}M"
            else:
                return f"R$ {value:,.0f}"
                
        elif format_type == "percentage":
            return f"{value:.1f}%"
            
        elif format_type == "number":
            if value >= 1000:
                return f"{value:,.0f}"
            else:
                return f"{value:.0f}"
                
        else:
            return str(value)
    
    def _get_kpi_icon(self, kpi_id: str) -> str:
        """Retorna ícone apropriado para cada KPI."""
        icon_map = {
            "volume-total-transacoes": "TrendingUp",
            "spread-medio": "DollarSign",
            "taxa-conversao": "Target",
            "ticket-medio": "CreditCard",
            "numero-clientes-ativos": "Users",
            "numero-operacoes": "Activity"
        }
        return icon_map.get(kpi_id, "BarChart")
    
    def _save_snapshot(self, snapshot: Dict[str, Any]):
        """Salva snapshot em arquivo JSON."""
        # Nome do arquivo com data
        filename = f"snapshot_{datetime.now().strftime('%Y%m%d')}.json"
        filepath = self.snapshot_dir / filename
        
        # Salvar snapshot datado
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(snapshot, f, indent=2, ensure_ascii=False)
        
        # Salvar também como "latest" para acesso rápido
        latest_path = self.snapshot_dir / "latest.json"
        with open(latest_path, 'w', encoding='utf-8') as f:
            json.dump(snapshot, f, indent=2, ensure_ascii=False)
            
        logger.info(f"💾 Snapshot salvo: {filepath}")
    
    def get_latest_snapshot(self) -> Optional[Dict[str, Any]]:
        """Retorna o snapshot mais recente."""
        latest_path = self.snapshot_dir / "latest.json"
        
        if latest_path.exists():
            with open(latest_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return None
    
    def _generate_error_snapshot(self, error_msg: str) -> Dict[str, Any]:
        """Gera snapshot de erro para fallback."""
        return {
            "metadata": {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "time": datetime.now().strftime("%H:%M:%S"),
                "error": True,
                "error_message": error_msg
            },
            "kpis": {},
            "summary": {
                "total_calculated": 0,
                "total_expected": 6,
                "success_rate": 0
            }
        }


# Função para uso em scripts e cron jobs
def generate_daily_snapshot_cli():
    """Função CLI para gerar snapshot diário."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    service = SnapshotService()
    snapshot = service.generate_daily_snapshot()
    
    if snapshot['metadata'].get('error'):
        logger.error(f"❌ Falha na geração do snapshot: {snapshot['metadata']['error_message']}")
        return 1
    else:
        logger.info(f"✅ Snapshot gerado com sucesso: {snapshot['summary']['total_calculated']} KPIs")
        return 0


if __name__ == "__main__":
    import sys
    sys.exit(generate_daily_snapshot_cli())
```

---

### 3️⃣ **API Endpoint para Snapshot**

**Arquivo**: `apps/backend/src/api/dashboard_snapshot.py`

```python
"""
Dashboard Snapshot API
======================

Endpoint otimizado para servir snapshots de KPIs.
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging

from src.services.snapshot_service import SnapshotService
from src.config.feature_flags import feature_flags

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])


@router.get("/snapshot")
async def get_dashboard_snapshot(
    client_id: str = "L2M",
    regenerate: bool = False
) -> Dict[str, Any]:
    """
    Retorna snapshot mais recente dos KPIs críticos.
    
    Args:
        client_id: Identificador do cliente
        regenerate: Se True, força regeneração do snapshot
        
    Returns:
        Dict com dados do snapshot
    """
    try:
        # Verificar se feature está habilitada
        if not feature_flags.is_enabled('dashboard_snapshot'):
            raise HTTPException(
                status_code=503,
                detail="Dashboard snapshot feature is disabled"
            )
        
        service = SnapshotService()
        
        # Se solicitado, regenerar snapshot (apenas em dev)
        if regenerate and feature_flags.is_enabled('dev_mode'):
            logger.info("🔄 Regenerando snapshot sob demanda")
            snapshot = service.generate_daily_snapshot(client_id)
        else:
            # Buscar snapshot existente
            snapshot = service.get_latest_snapshot()
            
            # Se não existir, gerar na primeira vez
            if not snapshot:
                logger.info("📸 Gerando snapshot inicial")
                snapshot = service.generate_daily_snapshot(client_id)
        
        # Verificar se houve erro
        if snapshot.get('metadata', {}).get('error'):
            raise HTTPException(
                status_code=500,
                detail=snapshot['metadata'].get('error_message', 'Unknown error')
            )
        
        # Retornar resposta formatada
        return {
            "success": True,
            "data": snapshot['kpis'],
            "metadata": {
                "generated_at": f"{snapshot['metadata']['date']} {snapshot['metadata']['time']}",
                "client_id": client_id,
                "kpi_count": snapshot['metadata']['kpi_count'],
                "cache_ttl": 3600,  # 1 hora
                "next_update": "03:00 BRT"
            },
            "summary": snapshot['summary']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao buscar snapshot: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard snapshot: {str(e)}"
        )


@router.get("/snapshot/info")
async def get_snapshot_info() -> Dict[str, Any]:
    """Retorna informações sobre o snapshot atual."""
    try:
        service = SnapshotService()
        snapshot = service.get_latest_snapshot()
        
        if not snapshot:
            return {
                "has_snapshot": False,
                "message": "No snapshot available"
            }
        
        return {
            "has_snapshot": True,
            "generated_at": f"{snapshot['metadata']['date']} {snapshot['metadata']['time']}",
            "kpi_count": snapshot['metadata'].get('kpi_count', 0),
            "success_rate": snapshot['summary'].get('success_rate', 0),
            "critical_kpis": list(snapshot.get('kpis', {}).keys())
        }
        
    except Exception as e:
        logger.error(f"Erro ao buscar info do snapshot: {e}")
        return {
            "has_snapshot": False,
            "error": str(e)
        }
```

---

### 4️⃣ **Script de Geração Diária**

**Arquivo**: `apps/backend/scripts/generate_daily_snapshot.py`

```python
#!/usr/bin/env python3
"""
Script para gerar snapshot diário dos KPIs críticos.

Uso:
    python scripts/generate_daily_snapshot.py [--client CLIENT_ID]
"""

import sys
import argparse
import logging
from pathlib import Path

# Adicionar diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.services.snapshot_service import SnapshotService

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(description='Gera snapshot diário de KPIs')
    parser.add_argument(
        '--client',
        default='L2M',
        help='ID do cliente (default: L2M)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Modo verboso'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        logger.info("🚀 Iniciando geração de snapshot diário")
        logger.info(f"📊 Cliente: {args.client}")
        
        service = SnapshotService()
        snapshot = service.generate_daily_snapshot(args.client)
        
        if snapshot['metadata'].get('error'):
            logger.error(f"❌ Erro: {snapshot['metadata']['error_message']}")
            return 1
        
        logger.info(f"✅ Snapshot gerado com sucesso!")
        logger.info(f"📈 KPIs calculados: {snapshot['summary']['total_calculated']}/{snapshot['summary']['total_expected']}")
        logger.info(f"📊 Taxa de sucesso: {snapshot['summary']['success_rate']:.1f}%")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Erro fatal: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
```

---

### 5️⃣ **Integração com Feature Flags**

**Atualizar**: `apps/backend/src/config/feature_flags.py`

```python
# Adicionar nova flag
self.flags = {
    'mvp_mode': os.getenv('MVP_MODE', 'true').lower() == 'true',
    'dashboard_snapshot': os.getenv('DASHBOARD_SNAPSHOT', 'true').lower() == 'true',  # NOVO
    'langgraph_agents': os.getenv('LANGGRAPH_ENABLED', 'false').lower() == 'true',
    'dashboard_real_data': os.getenv('DASHBOARD_REAL_DATA', 'true').lower() == 'true',
    'advanced_analytics': os.getenv('ADVANCED_ANALYTICS', 'false').lower() == 'true',
    'dev_mode': os.getenv('DEV_MODE', 'false').lower() == 'true'  # NOVO
}
```

---

### 6️⃣ **Configuração do Cron Job**

**Arquivo**: `apps/backend/cron/generate_snapshot.sh`

```bash
#!/bin/bash
# Script para cron job de geração de snapshot

# Carregar variáveis de ambiente
export $(cat /app/.env | xargs)

# Diretório do projeto
cd /app/apps/backend

# Executar geração de snapshot
python scripts/generate_daily_snapshot.py --client L2M >> logs/snapshot_generation.log 2>&1

# Verificar sucesso
if [ $? -eq 0 ]; then
    echo "[$(date)] ✅ Snapshot gerado com sucesso" >> logs/snapshot_generation.log
else
    echo "[$(date)] ❌ Erro na geração do snapshot" >> logs/snapshot_generation.log
    # Opcional: enviar notificação de erro
fi
```

**Crontab**:
```cron
# Gerar snapshot todos os dias às 3h da manhã (horário de Brasília)
0 3 * * * /app/apps/backend/cron/generate_snapshot.sh
```

---

## 🚀 Plano de Implementação

### **Dia 1: Setup e Configuração (4h)**
- [ ] Criar arquivo `critical_kpis.py` com os 6 KPIs
- [ ] Implementar `snapshot_service.py` básico
- [ ] Testar geração manual de snapshot
- [ ] Verificar que KPIs críticos existem no banco

### **Dia 2: Serviço Completo (6h)**
- [ ] Implementar formatação de valores
- [ ] Adicionar tratamento de erros robusto
- [ ] Criar testes unitários para o serviço
- [ ] Implementar método de fallback

### **Dia 3: API e Integração (4h)**
- [ ] Criar endpoint `/api/dashboard/snapshot`
- [ ] Integrar com feature flags
- [ ] Atualizar frontend para usar novo endpoint
- [ ] Testar performance (deve ser < 100ms)

### **Dia 4: Automação (3h)**
- [ ] Criar script de geração
- [ ] Configurar cron job
- [ ] Testar geração automática
- [ ] Documentar processo

### **Dia 5: Deploy e Monitoramento (3h)**
- [ ] Deploy no Railway
- [ ] Configurar logs estruturados
- [ ] Criar alertas para falhas
- [ ] Documentação final

---

## 📊 Resultado Esperado

### **Dashboard MVP com:**
- ✅ 6 KPIs críticos do mercado de câmbio
- ✅ Performance < 100ms (snapshot pré-calculado)
- ✅ Dados 100% reais do cliente L2M
- ✅ Atualização diária automática
- ✅ Zero complexidade desnecessária

### **KPIs Exibidos:**
1. **Volume Total**: R$ 2.9B
2. **Spread Médio FX**: 0.090%
3. **Taxa de Conversão**: 85.2%
4. **Ticket Médio**: R$ 1.2M
5. **Clientes Ativos**: 1,234
6. **Operações/Dia**: 89

---

## 🔧 Comandos Úteis

```bash
# Gerar snapshot manualmente
cd apps/backend
python scripts/generate_daily_snapshot.py

# Testar endpoint
curl http://localhost:8000/api/dashboard/snapshot

# Ver logs do cron
tail -f logs/snapshot_generation.log

# Forçar regeneração (dev only)
curl "http://localhost:8000/api/dashboard/snapshot?regenerate=true"
```

---

## ⚡ Vantagens desta Abordagem

1. **Reutiliza código existente**: Usa `KpiCalculationService` já implementado
2. **Sem hardcode**: Busca definições do banco
3. **Extensível**: Fácil adicionar mais KPIs no futuro
4. **Performance garantida**: Snapshot = resposta instantânea
5. **Manutenível**: Código limpo e bem organizado

**🎯 Resultado: MVP funcional em 5 dias com performance excelente!**