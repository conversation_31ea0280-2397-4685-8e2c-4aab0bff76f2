#!/usr/bin/env python3
"""
Test Fireworks API directly to verify connectivity and configuration
"""

import os
import asyncio
import requests
from datetime import datetime

def test_fireworks_sync():
    """Test Fireworks API with synchronous request"""
    print("🚀 Testing Fireworks API directly...")
    
    # Check if API key is available
    api_key = os.getenv('FIREWORKS_API_KEY')
    if not api_key:
        print("❌ FIREWORKS_API_KEY not found in environment")
        return False
    
    print(f"✅ Found FIREWORKS_API_KEY: {api_key[:10]}...")
    
    # Test API call
    url = "https://api.fireworks.ai/inference/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "accounts/fireworks/models/llama4-maverick-instruct-basic",
        "messages": [
            {
                "role": "user", 
                "content": "Test message: generate a simple SQL query to count records in a table called 'vendas'"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    try:
        print("📡 Making API call to Fireworks...")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ SUCCESS! Response content: {content[:200]}...")
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

async def test_fireworks_async():
    """Test Fireworks API with async client"""
    print("\n🔄 Testing Fireworks API with async client...")
    
    try:
        from openai import AsyncOpenAI
        
        api_key = os.getenv('FIREWORKS_API_KEY')
        if not api_key:
            print("❌ FIREWORKS_API_KEY not found")
            return False
        
        client = AsyncOpenAI(
            api_key=api_key,
            base_url="https://api.fireworks.ai/inference/v1"
        )
        
        response = await client.chat.completions.create(
            model="accounts/fireworks/models/llama4-maverick-instruct-basic",
            messages=[
                {"role": "user", "content": "Test: Generate SQL to count vendas"}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        content = response.choices[0].message.content
        print(f"✅ Async SUCCESS! Response: {content[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ Async request failed: {e}")
        return False

def check_environment_variables():
    """Check all required environment variables"""
    print("\n🔧 Checking environment variables...")
    
    required_vars = [
        'FIREWORKS_API_KEY',
        'DATABASE_URL',
        'TOGETHER_API_KEY',
        'ANTHROPIC_API_KEY',
        'GOOGLE_API_KEY'
    ]
    
    found_vars = []
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            found_vars.append(f"{var}: {value[:10]}...")
        else:
            missing_vars.append(var)
    
    print("✅ Found variables:")
    for var in found_vars:
        print(f"  - {var}")
    
    if missing_vars:
        print("⚠️ Missing variables:")
        for var in missing_vars:
            print(f"  - {var}")
    
    return len(missing_vars) == 0

async def main():
    """Run all tests"""
    print("🚄 Fireworks API Connectivity Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now()}")
    
    # Test environment variables
    env_ok = check_environment_variables()
    
    # Test sync API
    sync_ok = test_fireworks_sync()
    
    # Test async API  
    async_ok = await test_fireworks_async()
    
    print("\n📋 Test Summary:")
    print(f"  - Environment Variables: {'✅' if env_ok else '❌'}")
    print(f"  - Sync API Call: {'✅' if sync_ok else '❌'}")
    print(f"  - Async API Call: {'✅' if async_ok else '❌'}")
    
    if sync_ok and async_ok:
        print("\n🎉 All tests passed! Fireworks API is working correctly.")
    else:
        print("\n💥 Some tests failed. API connectivity issues detected.")

if __name__ == "__main__":
    asyncio.run(main())