#!/usr/bin/env python3
"""
Execute conversational chat migration on Railway PostgreSQL
"""

import os
import sys
import psycopg2
import subprocess

def get_railway_database_url():
    """Get DATABASE_URL_LEARNING from Railway variables"""
    result = subprocess.run(['railway', 'variables', '--json'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print("❌ Failed to get Railway variables")
        return None
    
    import json
    vars = json.loads(result.stdout)
    return vars.get('DATABASE_URL_LEARNING')

def execute_migration():
    """Execute the migration script on Railway PostgreSQL"""
    
    # Get Railway database URL
    db_url = get_railway_database_url()
    if not db_url:
        print("❌ DATABASE_URL_LEARNING not found in Railway variables")
        return False
    
    print(f"✅ Found Railway PostgreSQL URL")
    
    # Read migration script
    migration_path = os.path.join(os.path.dirname(__file__), 'conversational_chat_migration.sql')
    with open(migration_path, 'r') as f:
        migration_sql = f.read()
    
    print("📄 Migration script loaded")
    
    try:
        # Connect to Railway PostgreSQL
        print("🔗 Connecting to Railway PostgreSQL...")
        conn = psycopg2.connect(db_url)
        cur = conn.cursor()
        
        # Execute migration
        print("🚀 Executing migration...")
        cur.execute(migration_sql)
        conn.commit()
        
        # Verify tables were created
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE 'conversation_%'
            ORDER BY table_name
        """)
        
        tables = cur.fetchall()
        print(f"\n✅ Migration completed! Created {len(tables)} tables:")
        for table in tables:
            print(f"   - {table[0]}")
        
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    print("🚄 Railway PostgreSQL Migration Tool")
    print("=" * 40)
    
    if execute_migration():
        print("\n🎉 Migration successful!")
    else:
        print("\n😞 Migration failed!")
        sys.exit(1)