#!/usr/bin/env python3
"""
Test Groq API connectivity - the configured LLM provider
"""

import os
import requests
from datetime import datetime

def test_groq_api():
    """Test Groq API with the API key from Railway"""
    print("🚀 Testing Groq API (configured as DATAHERO_LLM_PROVIDER)...")
    
    # Use the API key from Railway
    api_key = "********************************************************"
    
    print(f"✅ Using GROQ_API_KEY: {api_key[:10]}...")
    
    # Test API call
    url = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "llama3-70b-8192",
        "messages": [
            {
                "role": "user", 
                "content": "Generate a simple SQL query to count records in a table called 'vendas' for 2023. Respond with only the SQL query."
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    try:
        print("📡 Making API call to Groq...")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ SUCCESS! SQL Generated: {content}")
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_fireworks_api():
    """Test Fireworks API to compare"""
    print("\n🔥 Testing Fireworks API (configured in llm.yaml)...")
    
    api_key = "fw_3Ze846V6V6iZQuFrQq2Jkk3C"
    
    print(f"✅ Using FIREWORKS_API_KEY: {api_key[:10]}...")
    
    # Test API call
    url = "https://api.fireworks.ai/inference/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "accounts/fireworks/models/llama4-maverick-instruct-basic",
        "messages": [
            {
                "role": "user", 
                "content": "Generate a simple SQL query to count records in a table called 'vendas' for 2023. Respond with only the SQL query."
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    try:
        print("📡 Making API call to Fireworks...")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ SUCCESS! SQL Generated: {content}")
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Run both API tests"""
    print("🚄 LLM Provider Connectivity Test")
    print("=" * 50)
    print(f"⏰ Test started at: {datetime.now()}")
    print()
    print("🔍 Issue identified: DATAHERO_LLM_PROVIDER=groq but llm.yaml uses fireworks")
    print("Testing both providers to identify the conflict...\n")
    
    # Test both APIs
    groq_ok = test_groq_api()
    fireworks_ok = test_fireworks_api()
    
    print("\n📋 Test Summary:")
    print(f"  - Groq API (default provider): {'✅' if groq_ok else '❌'}")
    print(f"  - Fireworks API (llm.yaml config): {'✅' if fireworks_ok else '❌'}")
    
    if groq_ok or fireworks_ok:
        print("\n🎯 Provider Conflict Analysis:")
        if groq_ok and not fireworks_ok:
            print("  ✅ Groq works, Fireworks fails - switch llm.yaml to Groq")
        elif fireworks_ok and not groq_ok:
            print("  ✅ Fireworks works, Groq fails - update DATAHERO_LLM_PROVIDER to fireworks")
        elif groq_ok and fireworks_ok:
            print("  ✅ Both work - configuration conflict needs resolution")
        
        print("\n💡 Recommended Action:")
        print("  - Either update llm.yaml to use Groq models")
        print("  - Or change DATAHERO_LLM_PROVIDER to 'fireworks'")
    else:
        print("\n💥 Both providers failed! Network or API key issues.")

if __name__ == "__main__":
    main()