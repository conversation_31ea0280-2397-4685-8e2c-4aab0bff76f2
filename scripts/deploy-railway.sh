#!/bin/bash

# Script de deploy para Railway - DataHero4
# Segue as melhores práticas identificadas na análise

set -e

echo "🚂 Iniciando deploy para Railway..."

# Verificar se está logado
echo "👤 Verificando autenticação..."
if ! railway whoami &>/dev/null; then
    echo "❌ Não autenticado. Execute: railway login"
    exit 1
fi

# Verificar se está no projeto correto
echo "📍 Verificando projeto..."
railway status

# Verificar se Redis está adicionado
echo "🗄️ Verificando Redis..."
if ! railway variables --service backend | grep -q "REDIS_URL"; then
    echo "⚠️  Redis não encontrado. Adicionando..."
    railway add redis
fi

# Verificar se PostgreSQL está adicionado
echo "🗄️ Verificando PostgreSQL..."
if ! railway variables --service backend | grep -q "DATABASE_URL"; then
    echo "⚠️  PostgreSQL não encontrado. Adicionando..."
    railway add postgresql
fi

# Definir variáveis de ambiente essenciais
echo "🔑 Configurando variáveis de ambiente..."
railway variables set USE_OPTIMIZED_WORKFLOW=true --service backend
railway variables set LOG_LEVEL=INFO --service backend
railway variables set PYTHONUNBUFFERED=1 --service backend
railway variables set PORT=8000 --service backend

# Verificar se as API keys estão configuradas
echo "🔐 Verificando API keys..."
required_vars=("TOGETHER_API_KEY" "ANTHROPIC_API_KEY" "OPENAI_API_KEY")
for var in "${required_vars[@]}"; do
    if ! railway variables --service backend | grep -q "$var"; then
        echo "❌ $var não encontrada. Configure com:"
        echo "   railway variables set $var=your_key_here --service backend"
        exit 1
    fi
done

# Deploy do backend
echo "🚀 Fazendo deploy do backend..."
railway up --service backend --detach

# Aguardar deploy do backend
echo "⏳ Aguardando deploy do backend..."
sleep 30

# Verificar saúde do backend
echo "🔍 Verificando saúde do backend..."
backend_url=$(railway domain --service backend)
if curl -f "$backend_url/health" &>/dev/null; then
    echo "✅ Backend está funcionando!"
else
    echo "❌ Backend não está respondendo. Verificando logs..."
    railway logs --service backend --tail 50
    exit 1
fi

# Deploy do frontend
echo "🚀 Fazendo deploy do frontend..."
railway up --service frontend --detach

# Aguardar deploy do frontend
echo "⏳ Aguardando deploy do frontend..."
sleep 30

# Verificar saúde do frontend
echo "🔍 Verificando saúde do frontend..."
frontend_url=$(railway domain --service frontend)
if curl -f "$frontend_url" &>/dev/null; then
    echo "✅ Frontend está funcionando!"
else
    echo "❌ Frontend não está respondendo. Verificando logs..."
    railway logs --service frontend --tail 50
    exit 1
fi

# Mostrar URLs finais
echo "🎉 Deploy concluído com sucesso!"
echo "🌐 URLs:"
echo "   Backend:  $backend_url"
echo "   Frontend: $frontend_url"
echo "   API Docs: $backend_url/docs"

echo "📊 Verificar métricas:"
echo "   railway logs --service backend"
echo "   railway logs --service frontend" 