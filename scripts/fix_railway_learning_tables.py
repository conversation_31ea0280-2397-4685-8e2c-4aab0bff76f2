#!/usr/bin/env python3
"""
Create learning tables in Railway PostgreSQL using environment variables
"""

import os
import sys
import subprocess
import json

# Add the backend src to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'apps', 'backend', 'src'))

def get_railway_env_vars():
    """Get Railway environment variables"""
    result = subprocess.run(['railway', 'variables', '--json'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print("❌ Failed to get Railway variables")
        return None
    
    return json.loads(result.stdout)

def create_tables():
    """Create learning tables in Railway PostgreSQL"""
    
    # Get Railway environment variables
    env_vars = get_railway_env_vars()
    if not env_vars:
        print("❌ Failed to get Railway environment variables")
        return False
    
    # Extract database connection info
    db_url = env_vars.get('DATABASE_URL_LEARNING')
    if not db_url:
        print("❌ DATABASE_URL_LEARNING not found in Railway variables")
        return False
    
    print(f"✅ Found Railway PostgreSQL URL")
    
    try:
        # Set environment variable for the backend code
        os.environ['DATABASE_URL_LEARNING'] = db_url
        
        # Import after setting environment
        from sqlalchemy import create_engine
        from models.learning_models import Base, create_all_tables
        
        print("🔗 Connecting to Railway PostgreSQL...")
        engine = create_engine(db_url, echo=True)
        
        print("🚀 Creating all learning tables...")
        create_all_tables(engine)
        
        print("✅ Tables created successfully!")
        
        # Verify tables were created
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('feedback_corrections', 'query_cache', 'query_history')
                ORDER BY table_name
            """))
            
            tables = result.fetchall()
            print(f"\n📋 Verified {len(tables)} learning tables:")
            for table in tables:
                print(f"   ✓ {table[0]}")
                
            # Check if patterns_extracted column exists
            result = conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'feedback_corrections' 
                AND column_name = 'patterns_extracted'
            """))
            
            patterns_col = result.fetchone()
            if patterns_col:
                print(f"   ✓ patterns_extracted column: {patterns_col[1]}")
            else:
                print("   ❌ patterns_extracted column missing!")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create tables: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚄 Railway PostgreSQL Learning Tables Setup")
    print("=" * 50)
    
    if create_tables():
        print("\n🎉 Learning tables setup successful!")
    else:
        print("\n😞 Learning tables setup failed!")
        sys.exit(1)