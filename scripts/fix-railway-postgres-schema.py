#!/usr/bin/env python3
"""
Fix Railway PostgreSQL schema - Add missing columns
"""

import os
import sys
import subprocess
import json

def get_railway_database_url():
    """Get DATABASE_URL_LEARNING from Railway variables"""
    result = subprocess.run(['railway', 'variables', '--json'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print("❌ Failed to get Railway variables")
        return None
    
    vars = json.loads(result.stdout)
    return vars.get('DATABASE_URL_LEARNING')

def fix_schema():
    """Add missing columns to Railway PostgreSQL"""
    
    # Get Railway database URL
    db_url = get_railway_database_url()
    if not db_url:
        print("❌ DATABASE_URL_LEARNING not found in Railway variables")
        return False
    
    print(f"✅ Found Railway PostgreSQL URL")
    
    # SQL to add missing columns
    migration_sql = """
    -- Add missing patterns_extracted column to feedback_corrections
    DO $$ 
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'feedback_corrections' 
            AND column_name = 'patterns_extracted'
        ) THEN
            ALTER TABLE feedback_corrections 
            ADD COLUMN patterns_extracted JSON DEFAULT '[]'::json;
            COMMENT ON COLUMN feedback_corrections.patterns_extracted IS 'Extracted patterns as JSON array';
            PRINT 'Added patterns_extracted column';
        ELSE
            PRINT 'patterns_extracted column already exists';
        END IF;
    EXCEPTION WHEN undefined_table THEN
        PRINT 'feedback_corrections table does not exist - creating it';
        
        -- Create the full table if it doesn't exist
        CREATE TABLE feedback_corrections (
            id VARCHAR(100) PRIMARY KEY DEFAULT ('fb_' || to_char(NOW(), 'YYYYMMDDHH24MISS') || '_' || substr(md5(random()::text), 1, 8)),
            question TEXT NOT NULL,
            original_query TEXT NOT NULL DEFAULT '',
            corrected_query TEXT NOT NULL DEFAULT '',
            explanation TEXT,
            feedback_type VARCHAR(20) NOT NULL,
            category VARCHAR(50),
            user_comment TEXT,
            client_id VARCHAR(50) DEFAULT 'L2M',
            sector VARCHAR(50) DEFAULT 'cambio',
            user_id VARCHAR(50),
            patterns_extracted JSON DEFAULT '[]'::json,
            meta_data JSON,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW(),
            applied_count INTEGER DEFAULT 0,
            success_rate FLOAT DEFAULT 0.0,
            query_cache_id VARCHAR(100)
        );
        
        -- Create indexes
        CREATE INDEX idx_feedback_question ON feedback_corrections(question);
        CREATE INDEX idx_feedback_created_at ON feedback_corrections(created_at);
        CREATE INDEX idx_feedback_applied_count ON feedback_corrections(applied_count);
        
        -- Add comments
        COMMENT ON TABLE feedback_corrections IS 'User feedback and corrections';
        COMMENT ON COLUMN feedback_corrections.patterns_extracted IS 'Extracted patterns as JSON array';
        
    END $$;
    
    -- Ensure query_cache table exists
    CREATE TABLE IF NOT EXISTS query_cache (
        id VARCHAR(100) PRIMARY KEY DEFAULT ('qc_' || to_char(NOW(), 'YYYYMMDDHH24MISS') || '_' || substr(md5(random()::text), 1, 8)),
        question TEXT NOT NULL,
        sql_query TEXT NOT NULL,
        question_embedding BYTEA,
        sql_embedding BYTEA,
        cached_results JSON,
        business_analysis JSON,
        suggestions JSON,
        similarity_threshold FLOAT DEFAULT 0.85,
        client_id VARCHAR(50) DEFAULT 'L2M',
        sector VARCHAR(50) DEFAULT 'cambio',
        meta_data JSON,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        access_count INTEGER DEFAULT 1,
        last_accessed TIMESTAMP DEFAULT NOW(),
        cache_version VARCHAR(10) DEFAULT '1.0'
    );
    
    -- Create indexes for query_cache
    CREATE INDEX IF NOT EXISTS idx_query_cache_question ON query_cache(question);
    CREATE INDEX IF NOT EXISTS idx_query_cache_client_sector ON query_cache(client_id, sector);
    CREATE INDEX IF NOT EXISTS idx_query_cache_created_at ON query_cache(created_at);
    CREATE INDEX IF NOT EXISTS idx_query_cache_access_count ON query_cache(access_count);
    
    -- Add foreign key constraint if both tables exist
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_feedback_query_cache'
        ) THEN
            ALTER TABLE feedback_corrections 
            ADD CONSTRAINT fk_feedback_query_cache 
            FOREIGN KEY (query_cache_id) REFERENCES query_cache(id);
        END IF;
    EXCEPTION WHEN others THEN
        -- Ignore if constraint already exists or other issues
        NULL;
    END $$;
    """
    
    try:
        # Use Railway connect to execute the migration
        print("🚀 Executing schema fix via Railway...")
        
        # Write SQL to temp file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as f:
            f.write(migration_sql)
            temp_file = f.name
        
        # Execute via Railway
        result = subprocess.run(['railway', 'run', 'psql', '--file', temp_file], 
                              capture_output=True, text=True)
        
        # Clean up temp file
        os.unlink(temp_file)
        
        if result.returncode == 0:
            print("✅ Schema fix completed successfully!")
            print(result.stdout)
            return True
        else:
            print(f"❌ Schema fix failed: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ Schema fix failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Railway PostgreSQL Schema Fix")
    print("=" * 40)
    
    if fix_schema():
        print("\n🎉 Schema fix successful!")
    else:
        print("\n😞 Schema fix failed!")
        sys.exit(1)