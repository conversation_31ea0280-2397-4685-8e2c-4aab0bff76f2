#!/usr/bin/env python3
"""
Fix Database Schema via Railway Environment
Uses the DataHero4 existing database connection tools
"""

import os
import sys
import logging

# Simulate Railway environment variables
os.environ['ENVIRONMENT'] = 'production'
os.environ['DATABASE_URL_LEARNING'] = 'postgresql://postgres:<EMAIL>:5432/railway'

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_schema_manually():
    """Manual fix approach using direct SQL execution"""
    
    print("🚄 DataHero4 Database Schema Fix Tool")
    print("=" * 50)
    
    # SQL statements to execute
    sql_statements = [
        # Create conversation_threads table
        """
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );
        """,
        
        # Create conversation_messages table
        """
        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            tokens_used INTEGER DEFAULT 0,
            model_used VARCHAR(100),
            response_time_ms INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSONB DEFAULT '{}'
        );
        """,
        
        # Add has_feedback column
        """
        DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'query_history' 
                AND column_name = 'has_feedback'
            ) THEN
                ALTER TABLE query_history 
                ADD COLUMN has_feedback BOOLEAN DEFAULT false;
            END IF;
        END $$;
        """,
        
        # Create indexes
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_client_sector 
        ON conversation_threads(client_id, sector);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_user 
        ON conversation_threads(user_id);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread 
        ON conversation_messages(thread_id);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_created 
        ON conversation_messages(created_at);
        """
    ]
    
    try:
        # Try to import the learning DB manager
        sys.path.append('apps/backend/src')
        from utils.learning_db_utils import get_db_manager
        
        db_manager = get_db_manager()
        if not db_manager:
            print("❌ Could not get database manager")
            return False
        
        print("✅ Database manager obtained")
        
        # Get connection and execute statements
        connection_string = db_manager.connection_string
        print(f"🔗 Using connection: {connection_string[:50]}...")
        
        # Execute each SQL statement
        for i, sql in enumerate(sql_statements, 1):
            try:
                print(f"📝 Executing SQL statement {i}/{len(sql_statements)}...")
                
                # Use db_manager's session to execute raw SQL
                with db_manager.get_session() as session:
                    session.execute(sql)
                    session.commit()
                    
                print(f"✅ Statement {i} executed successfully")
                
            except Exception as e:
                print(f"⚠️ Statement {i} failed (may already exist): {e}")
                continue
        
        print("\n🎉 Schema fix completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Could not import database utilities: {e}")
        print("💡 This script needs to run in the Railway environment")
        return False
    except Exception as e:
        print(f"❌ Schema fix failed: {e}")
        return False

def create_railway_script():
    """Create a script to be executed in Railway environment"""
    
    railway_script = '''#!/usr/bin/env python3
"""
Execute this script in Railway console to fix database schema
"""

import os
import sys
sys.path.append('/app/src')

from utils.learning_db_utils import get_db_manager
from sqlalchemy import text

def fix_schema():
    """Fix database schema in Railway environment"""
    
    print("🚄 DataHero4 Database Schema Fix (Railway)")
    print("=" * 50)
    
    db_manager = get_db_manager()
    if not db_manager:
        print("❌ Could not get database manager")
        return False
    
    sql_statements = [
        """
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            tokens_used INTEGER DEFAULT 0,
            model_used VARCHAR(100),
            response_time_ms INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSONB DEFAULT '{}'
        );
        """,
        """
        DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'query_history' 
                AND column_name = 'has_feedback'
            ) THEN
                ALTER TABLE query_history 
                ADD COLUMN has_feedback BOOLEAN DEFAULT false;
            END IF;
        END $$;
        """
    ]
    
    for i, sql in enumerate(sql_statements, 1):
        try:
            print(f"📝 Executing statement {i}/{len(sql_statements)}...")
            with db_manager.get_session() as session:
                session.execute(text(sql))
                session.commit()
            print(f"✅ Statement {i} completed")
        except Exception as e:
            print(f"⚠️ Statement {i} warning: {e}")
    
    print("🎉 Schema fix completed!")

if __name__ == "__main__":
    fix_schema()
'''
    
    # Write the railway script
    with open('railway_schema_fix.py', 'w') as f:
        f.write(railway_script)
    
    print("📁 Created railway_schema_fix.py")
    print("\n💡 To execute in Railway:")
    print("1. Copy railway_schema_fix.py to Railway environment")  
    print("2. Run: railway run python railway_schema_fix.py")

if __name__ == "__main__":
    print("🚀 Attempting local schema fix (will work only if DB accessible)...")
    
    success = fix_schema_manually()
    
    if not success:
        print("\n📝 Creating Railway-compatible script...")
        create_railway_script()
        
        print("\n💭 Alternative: Manual SQL execution needed in Railway console")
        print("The following SQL should be executed in Railway PostgreSQL:")
        print("\n" + "="*50)
        with open('fix_schema.sql', 'r') as f:
            print(f.read())
        print("="*50)