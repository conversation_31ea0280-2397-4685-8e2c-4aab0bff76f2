#!/bin/bash

# Script para corrigir problemas específicos do Railway
# Baseado na análise dos problemas identificados

set -e

echo "🔧 Corrigindo problemas do Railway..."

# 1. Verificar se o ponto de entrada está correto
echo "📍 Verificando ponto de entrada..."
if [ -f "apps/backend/webapp.py" ]; then
    echo "✅ webapp.py existe"
else
    echo "❌ webapp.py não encontrado. Criando..."
    # O arquivo já foi criado pelos comandos anteriores
fi

# 2. Verificar se langgraph.json existe
echo "📋 Verificando langgraph.json..."
if [ -f "apps/backend/langgraph.json" ]; then
    echo "✅ langgraph.json existe"
else
    echo "❌ langgraph.json não encontrado. Criando..."
    # O arquivo já foi criado pelos comandos anteriores
fi

# 3. Verificar Poetry dependencies
echo "📦 Verificando dependências Poetry..."
cd apps/backend
if poetry show uvicorn &>/dev/null; then
    echo "✅ uvicorn está instalado"
else
    echo "❌ uvicorn não encontrado. Instalando..."
    poetry add uvicorn[standard]
fi

# 4. Verificar se gunicorn está instalado (alternativa)
if poetry show gunicorn &>/dev/null; then
    echo "✅ gunicorn está instalado"
else
    echo "⚠️  gunicorn não encontrado. Instalando como alternativa..."
    poetry add gunicorn
fi

# 5. Testar importação do app
echo "🧪 Testando importação do app..."
if poetry run python -c "from webapp import app; print('✅ webapp.app importado com sucesso')" 2>/dev/null; then
    echo "✅ Importação OK"
else
    echo "❌ Erro na importação. Verificando..."
    poetry run python -c "from webapp import app" || {
        echo "❌ Erro crítico na importação. Verificar logs:"
        poetry run python -c "import sys; sys.path.append('src'); from interfaces.api import app; print('✅ Importação alternativa OK')"
    }
fi

# 6. Verificar se o arquivo de configuração Railway está correto
echo "🚂 Verificando railway.toml..."
if grep -q "webapp:app" ../../railway.toml; then
    echo "✅ railway.toml configurado corretamente"
else
    echo "❌ railway.toml precisa ser atualizado"
    echo "   Comando de start deve ser: poetry run uvicorn webapp:app --host 0.0.0.0 --port \$PORT --workers 2"
fi

# 7. Verificar variáveis de ambiente essenciais
echo "🔑 Verificando variáveis de ambiente..."
required_vars=("DATABASE_URL" "REDIS_URL" "TOGETHER_API_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "⚠️  $var não está definida localmente"
    else
        echo "✅ $var está definida"
    fi
done

# 8. Verificar se o porto está configurado
echo "🔌 Verificando configuração de porta..."
if grep -q "PORT" ../../railway.toml; then
    echo "✅ PORT configurada no railway.toml"
else
    echo "❌ PORT não configurada"
fi

# 9. Criar arquivo de health check
echo "🏥 Criando health check..."
cat > health_check.py << 'EOF'
#!/usr/bin/env python3
"""Health check script for Railway deployment."""

import sys
import requests
import time

def check_health(url="http://localhost:8000/health", timeout=30):
    """Check if the application is healthy."""
    for i in range(timeout):
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ Health check passed: {response.json()}")
                return True
        except Exception as e:
            print(f"⏳ Attempt {i+1}/{timeout}: {e}")
            time.sleep(1)
    return False

if __name__ == "__main__":
    url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000/health"
    if check_health(url):
        sys.exit(0)
    else:
        print("❌ Health check failed")
        sys.exit(1)
EOF

chmod +x health_check.py

cd ../..

echo "✅ Correções aplicadas!"
echo "📝 Próximos passos:"
echo "   1. Testar localmente: cd apps/backend && poetry run uvicorn webapp:app --host 0.0.0.0 --port 8000"
echo "   2. Fazer deploy: railway up --service backend"
echo "   3. Verificar logs: railway logs --service backend" 