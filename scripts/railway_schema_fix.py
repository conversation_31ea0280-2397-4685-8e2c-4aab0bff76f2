#!/usr/bin/env python3
"""
Execute this script in Railway console to fix database schema
"""

import os
import sys
sys.path.append('/app/src')

from utils.learning_db_utils import get_db_manager
from sqlalchemy import text

def fix_schema():
    """Fix database schema in Railway environment"""
    
    print("🚄 DataHero4 Database Schema Fix (Railway)")
    print("=" * 50)
    
    db_manager = get_db_manager()
    if not db_manager:
        print("❌ Could not get database manager")
        return False
    
    sql_statements = [
        """
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            tokens_used INTEGER DEFAULT 0,
            model_used VARCHAR(100),
            response_time_ms INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSONB DEFAULT '{}'
        );
        """,
        """
        DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'query_history' 
                AND column_name = 'has_feedback'
            ) THEN
                ALTER TABLE query_history 
                ADD COLUMN has_feedback BOOLEAN DEFAULT false;
            END IF;
        END $$;
        """
    ]
    
    for i, sql in enumerate(sql_statements, 1):
        try:
            print(f"📝 Executing statement {i}/{len(sql_statements)}...")
            with db_manager.get_session() as session:
                session.execute(text(sql))
                session.commit()
            print(f"✅ Statement {i} completed")
        except Exception as e:
            print(f"⚠️ Statement {i} warning: {e}")
    
    print("🎉 Schema fix completed!")

if __name__ == "__main__":
    fix_schema()
