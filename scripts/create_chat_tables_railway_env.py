#!/usr/bin/env python3
"""
Script para criar tabelas de chat no PostgreSQL Railway via ambiente
Usa a variável DATABASE_URL_LEARNING do Railway
"""
import psycopg2
import os

def create_tables():
    # Obter connection string do ambiente Railway
    connection_string = os.getenv('DATABASE_URL_LEARNING')
    
    if not connection_string:
        print("❌ DATABASE_URL_LEARNING não encontrada")
        return
    
    print(f"🔗 Conectando ao banco: {connection_string[:50]}...")
    
    conn = None
    try:
        # Conectar ao banco
        conn = psycopg2.connect(connection_string)
        conn.autocommit = True
        cur = conn.cursor()
        
        print("🔗 Conectado ao banco Railway...")
        
        # Criar tabelas
        sql = """
        -- Criar tabelas para o sistema de chat avançado
        CREATE TABLE IF NOT EXISTS conversation_threads (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id VARCHAR(255) NOT NULL,
            client_id VARCHAR(255) NOT NULL,
            sector VARCHAR(255) NOT NULL,
            title VARCHAR(255),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_message_at TIMESTAMP,
            message_count INTEGER DEFAULT 0,
            total_tokens_used INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT true,
            metadata JSONB DEFAULT '{}'
        );

        CREATE TABLE IF NOT EXISTS conversation_messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            thread_id UUID NOT NULL REFERENCES conversation_threads(id) ON DELETE CASCADE,
            role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            tokens_used INTEGER DEFAULT 0,
            model_used VARCHAR(100),
            response_time_ms INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSONB DEFAULT '{}'
        );

        CREATE INDEX IF NOT EXISTS idx_conversation_threads_client_sector ON conversation_threads(client_id, sector);
        CREATE INDEX IF NOT EXISTS idx_conversation_threads_user ON conversation_threads(user_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_thread ON conversation_messages(thread_id);
        CREATE INDEX IF NOT EXISTS idx_conversation_messages_created ON conversation_messages(created_at);
        """
        
        cur.execute(sql)
        print("✅ Tabelas criadas com sucesso no Railway!")
        
        # Verificar tabelas criadas
        cur.execute("SELECT COUNT(*) FROM conversation_threads;")
        result = cur.fetchone()
        count = result[0] if result else 0
        print(f"📊 Tabela conversation_threads: {count} registros")
        
        # Inserir um registro de teste
        cur.execute("""
            INSERT INTO conversation_threads (user_id, client_id, sector, title, description)
            VALUES ('test_user', 'L2M', 'cambio', 'Thread de Teste Railway', 'Thread criada para testar o sistema Railway')
            ON CONFLICT DO NOTHING;
        """)
        
        cur.execute("SELECT COUNT(*) FROM conversation_threads;")
        result = cur.fetchone()
        final_count = result[0] if result else 0
        print(f"📊 Total de threads após teste: {final_count}")
        
        cur.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    create_tables() 