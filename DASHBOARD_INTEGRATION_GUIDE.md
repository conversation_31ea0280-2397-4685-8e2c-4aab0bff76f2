# 📊 Dashboard Integration Guide

## Overview

Complete integration guide for the DataHero4 dashboard system, featuring real-time KPI calculation, natural language editing, and enterprise-grade visualizations.

## 🎯 Key Features

### Real-Time KPI System
- **34 KPIs** across 7 categories (Volume, Performance, Operations, Risk, Quality, Efficiency, Growth)
- **Live SQL execution** with period filters and client-specific data
- **Intelligent caching** for optimal performance
- **Auto-prioritization** of critical business metrics

### Natural Language Dashboard Editing
- **BusinessAnalyst integration** for command understanding
- **Chart type modifications** (line, bar, area, gauge, pie)
- **Time period adjustments** (daily, weekly, monthly, yearly)
- **KPI management** (add, remove, reorder)
- **Alert threshold configuration**

### Performance Optimizations
- **Lazy loading** to prevent circular imports
- **Parallel KPI computation** for faster dashboard loading
- **Confidence-based routing** to skip unnecessary validations
- **Multi-layer caching** (PostgreSQL, Redis, in-memory)

## 🏗️ Architecture

### Backend Components

#### 1. Dashboard API Router (`src/interfaces/dashboard_api.py`)
```python
# Main endpoints
GET /api/dashboard/dashboard     # Complete dashboard data
GET /api/dashboard/kpis         # All KPIs with filters
GET /api/dashboard/kpi/{id}     # Single KPI details
GET /api/dashboard/alerts       # Alert management
POST /api/dashboard/edit        # Natural language editing
GET /api/dashboard/health       # System health check
```

#### 2. KPI Calculator Service (`src/services/kpi_calculator.py`)
- **SQL Generation**: Dynamic query building based on KPI configuration
- **Period Filtering**: Current month, last month, YTD, custom ranges
- **Data Processing**: Value formatting, chart data generation, trend analysis
- **Alert Detection**: Threshold-based alert triggering

#### 3. Dashboard Service (`src/services/dashboard_service.py`)
- **KPI Aggregation**: Combines individual KPIs into dashboard structure
- **Priority Management**: Categorizes KPIs by business importance
- **Alert System**: Intelligent alert generation with severity levels
- **Natural Language Processing**: Command parsing and dashboard modifications

### Frontend Components

#### 1. API Client (`apps/frontend/src/lib/api.ts`)
```typescript
// Dashboard API functions
getDashboard(clientId, sector, period, priorityOnly, includeAlerts)
getKPIs(clientId, sector, period, category, priorityOnly)
getKPI(kpiId, clientId, sector, period)
getDashboardAlerts(clientId, sector, severity, activeOnly)
editDashboard(clientId, sector, command, context)
```

#### 2. React Hook (`apps/frontend/src/hooks/useKpiData.ts`)
- **Real-time data fetching** from dashboard API
- **Data transformation** from API format to frontend format
- **Error handling** with retry logic
- **Loading states** for optimal UX
- **Caching** with react-query integration

#### 3. Dashboard Components (`apps/frontend/src/components/dashboard/`)
- **KpiCard**: Individual KPI visualization with charts
- **KpiGrid**: Responsive grid layout for KPI cards
- **EditKpiModal**: Natural language editing interface
- **AlertModal**: Alert management and notifications
- **DashboardControls**: Time period and filter controls

## 📋 Configuration

### KPI Configuration (`src/config/setores/cambio/kpis-exchange-json.json`)
```json
{
  "metadata": {
    "version": "1.0",
    "sector": "exchange",
    "total_kpis": 34
  },
  "categories": [
    {
      "id": "volume_metrics",
      "name": "Volume Metrics",
      "kpis": [
        {
          "id": "total_volume",
          "name": "Volume Total Negociado",
          "description": "Volume total de operações de câmbio (USD)",
          "formula": "SUM(valor_moeda_estrangeira)",
          "unit": "Valor monetário (R$, US$, etc.)",
          "frequency": "Diário",
          "importance": "high"
        }
      ]
    }
  ]
}
```

### Environment Variables
```bash
# Database connectivity for KPI calculation
DATABASE_URL=postgresql://user:pass@host:port/db

# LLM providers for natural language processing
TOGETHER_API_KEY=your_together_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Cache optimization
REDIS_URL=redis://localhost:6379
```

## 🚀 Quick Start

### 1. Start Development Environment
```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:backend  # Backend on port 8000
npm run dev:frontend # Frontend on port 3000
```

### 2. Access Dashboard
- **Frontend Dashboard**: http://localhost:3000/dashboard
- **API Documentation**: http://localhost:8000/docs
- **Dashboard API**: http://localhost:8000/api/dashboard/

### 3. Test KPI Calculation
```bash
# Test single KPI
curl "http://localhost:8000/api/dashboard/kpi/total_volume?client_id=L2M&sector=cambio"

# Test complete dashboard
curl "http://localhost:8000/api/dashboard/dashboard?client_id=L2M&sector=cambio&period=current_month"
```

### 4. Test Natural Language Editing
```bash
curl -X POST "http://localhost:8000/api/dashboard/edit" \
  -H "Content-Type: application/json" \
  -d '{
    "client_id": "L2M",
    "sector": "cambio", 
    "command": "Change the volume chart to show weekly data"
  }'
```

## 🧪 Testing

### Integration Tests
```bash
# Run dashboard integration tests
python test_dashboard_final.py

# Run comprehensive validation
python validation_dashboard_integration.py
```

### Manual Testing Checklist
- [ ] Dashboard loads with real KPI data
- [ ] All 34 KPIs calculate correctly
- [ ] Charts render with appropriate data
- [ ] Natural language editing works
- [ ] Alerts display with correct severity
- [ ] Time period filters work
- [ ] Performance is acceptable (<3s load time)

## 🔧 Troubleshooting

### Common Issues

#### 1. KPI Calculation Errors
**Problem**: KPIs showing "N/A" or error values
**Solution**: 
- Check database connectivity
- Verify KPI configuration syntax
- Review SQL query generation logs

#### 2. Natural Language Editing Not Working
**Problem**: Edit commands not being processed
**Solution**:
- Verify BusinessAnalyst agent configuration
- Check LLM provider API keys
- Review command parsing logic

#### 3. Frontend Dashboard Not Loading
**Problem**: Dashboard page shows loading state indefinitely
**Solution**:
- Check API endpoint connectivity
- Verify CORS configuration
- Review browser console for errors

#### 4. Performance Issues
**Problem**: Dashboard takes >5 seconds to load
**Solution**:
- Enable Redis caching
- Check database query performance
- Verify lazy loading implementation

### Debug Endpoints

```bash
# Check dashboard API health
curl http://localhost:8000/api/dashboard/health

# Get specific KPI debug info
curl "http://localhost:8000/api/dashboard/kpi/total_volume?client_id=L2M&sector=cambio&debug=true"

# Test SQL generation
curl "http://localhost:8000/debug/sql-generation?kpi_id=total_volume&period=current_month"
```

## 📈 Performance Metrics

### Target Performance
- **Dashboard Load Time**: <2 seconds for cached data
- **KPI Calculation**: <500ms per KPI
- **Natural Language Processing**: <1 second response time
- **Memory Usage**: <100MB additional for dashboard services

### Monitoring
- **API Response Times**: Monitor `/api/dashboard/*` endpoints
- **Database Query Performance**: Track KPI SQL execution times
- **Cache Hit Rates**: Monitor Redis/PostgreSQL cache effectiveness
- **Error Rates**: Track dashboard API error responses

## 🔄 Future Enhancements

### Planned Features
1. **Real-time WebSocket Updates**: Live KPI refresh without page reload
2. **Advanced Charting**: Interactive charts with drill-down capabilities
3. **Custom Dashboard Layouts**: User-configurable dashboard arrangements
4. **Export Functionality**: PDF/Excel export of dashboard data
5. **Mobile Optimization**: Responsive design for mobile devices

### Integration Opportunities
1. **WhatsApp Dashboard**: KPI delivery via WhatsApp messages
2. **Slack Integration**: Dashboard alerts in Slack channels
3. **Email Reports**: Scheduled dashboard reports via email
4. **Third-party BI Tools**: Integration with Tableau, Power BI

## 📚 Related Documentation

- [CLAUDE.md](./CLAUDE.md) - Main project documentation
- [API.md](./docs/API.md) - Complete API reference
- [DEPLOYMENT.md](./docs/DEPLOYMENT.md) - Production deployment guide
- [TESTING.md](./docs/testing/) - Comprehensive testing strategy

---

**📊 Dashboard Integration Complete!**
*Ready for production deployment with enterprise-grade performance and scalability.*