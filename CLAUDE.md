# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DataHero4 is a monorepo-based conversational AI data analysis system that translates natural language questions into SQL queries, providing insights, visualizations, and follow-up suggestions. Built with FastAPI/LangGraph (backend) and React/TypeScript (frontend).

## Essential Commands

### Development
```bash
# Start both backend and frontend
npm run dev

# Backend only (port 8000)
npm run dev:backend

# Frontend only (port 3000)
npm run dev:frontend

# Backend development (from apps/backend/)
poetry run python src/main.py "question" --client-id L2M --sector cambio
```

### Testing
```bash
# Run all tests
npm run test

# Backend tests (from apps/backend/)
poetry run pytest
poetry run pytest -v  # verbose
poetry run pytest tests/unit/  # specific directory

# Frontend tests (from apps/frontend/)
npm run test
```

### Build & Lint
```bash
# Build all projects
npm run build

# TypeScript validation
npm run typecheck

# Lint all code
npm run lint
```

### CLI Tools (from apps/backend/)
```bash
poetry run datahero4          # Enhanced chat CLI
poetry run datahero4-simple   # Simple chat CLI  
poetry run datahero4-feedback # Feedback CLI
```

## Dependency Management

- **Always use Poetry for Python dependencies** in the backend (apps/backend/)
- Use npm/yarn for frontend dependencies (apps/frontend/)
- The monorepo uses Turbo for orchestrating builds/tests

## High-Level Architecture

### Backend Pipeline (LangGraph State Machine)
The system uses an optimized workflow with these key components:

1. **Multi-Agent System**:
   - Coordinator Agent: Orchestrates the pipeline
   - Query Generator Agent: NL to SQL conversion
   - Query Validator Agent: SQL validation and correction
   - Business Analyst Agent: Generates insights
   - Question Suggester Agent: Creates follow-up questions

2. **Performance Optimizations**:
   - Ultra-fast caching: 1.5s response time (85% improvement)
   - Parallel execution for independent nodes
   - Confidence-based routing
   - Auto-correction for 20% of SQL queries

3. **Context Preservation**:
   - Maintains conversation context across queries
   - Segment-level context management (based on SeCom paper)
   - Entity extraction and reference resolution
   - Feedback learning system

### Frontend Architecture
- React 18 with TypeScript
- State management: Zustand + TanStack Query
- UI components: shadcn/ui with Tailwind CSS
- Real-time chat via WebSocket
- Animations with Motion.dev

### Key Directories
```
apps/backend/
├── src/
│   ├── agents/      # LLM agents (coordinator, query generator, etc.)
│   ├── graphs/      # LangGraph workflows and routing
│   ├── nodes/       # Pipeline nodes (cache, validation, etc.)
│   ├── interfaces/  # API endpoints
│   ├── caching/     # Cache implementation
│   └── config/      # Client/sector configurations
└── tests/          # Test suites

apps/frontend/
├── src/
│   ├── components/  # UI components
│   ├── pages/       # Route pages
│   └── hooks/       # Custom React hooks
└── tests/
```

## Configuration

### Environment Variables
Backend requires:
- `DATABASE_URL`: PostgreSQL connection
- `TOGETHER_API_KEY`: LLM provider
- `ANTHROPIC_API_KEY`: Optional LLM provider
- `GOOGLE_API_KEY`: Optional LLM provider

### Client/Sector Configuration
Each client has specific configurations in:
```
apps/backend/src/config/setores/{sector}/{client}/
├── nivel2/          # Level 2 optimizations
├── prompts/         # Custom prompts
└── kpis-{sector}.json
```

## Testing Strategy

- **Unit tests**: Individual agents and utilities
- **Integration tests**: Full pipeline execution
- **Performance tests**: Latency benchmarks
- **Regression tests**: Seven Golden Questions
- Minimum coverage: 80%

## Important Notes

1. The system exclusively uses the `optimized_workflow` pipeline
2. Cache invalidation happens automatically on user feedback
3. Parallel nodes execute simultaneously for performance
4. Context preservation maintains temporal and business entities
5. The SQL validator accepts IN clauses for temporal comparisons

## Best Practices

- não crie arquivos no root - mantenha neles apenas os necessários, deixando os demais em cada uma de suas pastas

## Project Requirements

- É inegociável manter nesse projeto:
  - Framework de agentes LangGraph
  - LLM calls para generation + analysis
  - Validation robusta
  - Entity extraction inteligente

## Troubleshooting Guidelines

- quando não conseguir fazer algo relacionado a alguma dependencia ou serviço externo, consulte a documentacao atual com context7 para saber como fazer