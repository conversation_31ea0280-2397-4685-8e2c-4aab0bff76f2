#!/usr/bin/env python3
"""
Test LLM Provider directly to verify it's working
"""

import os
import sys
import asyncio

# Add backend source to path
sys.path.append('apps/backend/src')

# Set environment variables as they are in Railway
os.environ['FIREWORKS_API_KEY'] = 'fw_3Ze846V6V6iZQuFrQq2Jkk3C'
os.environ['GROQ_API_KEY'] = '********************************************************'
os.environ['ENVIRONMENT'] = 'production'

async def test_llm_provider():
    """Test LLM Provider with L2M configuration"""
    print("🚀 Testing LLM Provider directly...")
    print("=" * 50)
    
    try:
        from tools.llm_provider import LLMProvider
        
        # Test query_generator_agent (the one that generates SQL)
        print("\n📝 Testing query_generator_agent...")
        provider = LLMProvider(
            setor="cambio",
            cliente="L2M", 
            agent_name="query_generator_agent"
        )
        
        print(f"Provider: {provider.provider}")
        print(f"Model: {provider.model}")
        print(f"Temperature: {provider.temperature}")
        
        # Simple test prompt
        prompt = """Given the schema:
        - Table: vendas (columns: id, valor, data, ano)
        
        Generate SQL for: "total de vendas em 2023"
        
        Return only the SQL query."""
        
        print("\n🔄 Calling LLM...")
        response = await provider.ainvoke(prompt)
        
        print(f"\n✅ Response received:")
        print(response)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_sync_version():
    """Test sync invoke method"""
    print("\n\n📝 Testing sync invoke method...")
    print("=" * 50)
    
    try:
        from tools.llm_provider import LLMProvider
        
        provider = LLMProvider(
            setor="cambio",
            cliente="L2M", 
            agent_name="query_generator_agent"
        )
        
        prompt = "Generate SQL to count records in table vendas"
        
        print("🔄 Calling sync invoke...")
        
        # Try sync version which is used in the pipeline
        response = provider.invoke(prompt)
        
        print(f"\n✅ Sync response received:")
        print(response)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Sync error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🚄 DataHero4 LLM Provider Direct Test")
    print("Testing configuration as deployed in Railway")
    print()
    
    # Test async version
    async_ok = await test_llm_provider()
    
    # Test sync version
    sync_ok = await test_sync_version()
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  - Async invoke: {'✅' if async_ok else '❌'}")
    print(f"  - Sync invoke: {'✅' if sync_ok else '❌'}")
    
    if async_ok or sync_ok:
        print("\n✅ LLM Provider is working!")
    else:
        print("\n❌ LLM Provider has issues!")

if __name__ == "__main__":
    asyncio.run(main())