---
type: "manual"
---

Quando o usuário solicitar ajuda para rodar o DataHero4 localmente, siga estas instruções: O projeto usa Turbo para gerenciar o monorepo com backend (FastAPI + LangGraph) e frontend (React + Vite). Para desenvolvimento, use ./scripts/dev.sh (método recomendado que inicia backend + frontend automaticamente com logs prefixados [BACKEND] e [FRONTEND]) ou os comandos Turbo da raiz: npm run dev (backend + frontend em paralelo), npm run dev:backend (apenas backend na porta 8000), ou npm run dev:frontend (apenas frontend na porta 3000). URLs de desenvolvimento: backend em http://localhost:8000, frontend em http://localhost:3000, docs em http://localhost:8000/docs. O script dev.sh é mais robusto pois fornece logs organizados e controle melhor dos processos. Outros comandos úteis: npm run test (todos os testes), npm run build (build completo), npm run lint (linting), npm run typecheck (verificação de tipos). Use Ctrl+C para parar os servidores.