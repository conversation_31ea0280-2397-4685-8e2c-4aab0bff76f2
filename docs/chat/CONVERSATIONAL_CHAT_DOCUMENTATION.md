# DataHero4 Conversational Chat - Documentação Completa

## 📋 Índice

1. [Visão Geral](#visão-geral)
2. [Arquitetura do Sistema](#arquitetura-do-sistema)
3. [Componentes Principais](#componentes-principais)
4. [API Reference](#api-reference)
5. [Frontend Components](#frontend-components)
6. [Database Schema](#database-schema)
7. [Deployment](#deployment)
8. [Testing](#testing)
9. [Monitoring & Logging](#monitoring--logging)
10. [Troubleshooting](#troubleshooting)

## 🎯 Visão Geral

O **DataHero4 Conversational Chat** é um sistema completo de chat conversacional que integra com o pipeline de análise de dados existente do DataHero4. Oferece uma interface ChatGPT-like para interação natural com dados empresariais.

### Principais Funcionalidades

- ✅ **Interface Conversacional**: Chat em tempo real com streaming de respostas
- ✅ **Integração LangGraph**: Pipeline completo de análise de dados
- ✅ **Gerenciamento de Threads**: Múltiplas conversas organizadas
- ✅ **Streaming Real**: Respostas em tempo real com SSE
- ✅ **Performance Otimizada**: Cache semântico e otimização de queries
- ✅ **Error Handling**: Tratamento abrangente de erros
- ✅ **Monitoring**: Logging detalhado e métricas

### Tecnologias Utilizadas

**Backend:**
- FastAPI (Python 3.9+)
- PostgreSQL (Supabase)
- LangGraph para workflow
- Gemini 2.0 Flash para LLM
- SSE para streaming

**Frontend:**
- React 18 + TypeScript
- Zustand para estado global
- Tailwind CSS para styling
- React Query para data fetching

## 🏗️ Arquitetura do Sistema

```mermaid
graph TB
    subgraph "Frontend"
        UI[React Chat Interface]
        Store[Zustand Store]
        Components[Chat Components]
    end
    
    subgraph "Backend API"
        API[FastAPI Endpoints]
        Engine[GeminiChatEngine]
        DB[SimpleConversationDB]
    end
    
    subgraph "Core Services"
        LG[LangGraph Workflow]
        Cache[Query Optimizer]
        Logger[Logging System]
        Error[Error Handler]
    end
    
    subgraph "External"
        Supabase[(Supabase DB)]
        Gemini[Gemini 2.0 Flash]
    end
    
    UI --> Store
    Store --> API
    API --> Engine
    Engine --> LG
    Engine --> DB
    DB --> Supabase
    LG --> Gemini
    API --> Cache
    API --> Logger
    API --> Error
```

## 🔧 Componentes Principais

### Backend Components

#### 1. GeminiChatEngine
**Localização**: `apps/backend/src/services/gemini_chat_engine.py`

Engine principal que integra com LangGraph para processamento de mensagens.

```python
from src.services.gemini_chat_engine import GeminiChatEngine

# Inicialização
engine = GeminiChatEngine(db_manager)

# Envio de mensagem com streaming
async for event in engine.send_message(
    thread_id="thread-123",
    user_message="Mostre as vendas do último mês",
    user_id="user-456",
    stream=True
):
    print(event)
```

#### 2. SimpleConversationDB
**Localização**: `apps/backend/src/utils/simple_conversation_db.py`

Gerenciador de banco de dados para threads e mensagens.

```python
from src.utils.simple_conversation_db import SimpleConversationDB

# Inicialização
db = SimpleConversationDB(connection_string)

# Operações básicas
thread = db.create_thread(thread_data)
messages = db.list_thread_messages(thread_id)
stats = db.get_conversation_stats(user_id)
```

#### 3. QueryOptimizer
**Localização**: `apps/backend/src/utils/query_optimizer.py`

Sistema de cache semântico e otimização de queries.

```python
from src.utils.query_optimizer import query_optimizer, cached_query

# Uso do cache
@cached_query(ttl=3600)
def expensive_query():
    return db.execute_complex_query()

# Estatísticas
stats = query_optimizer.get_cache_stats()
```

#### 4. Error Handler
**Localização**: `apps/backend/src/utils/error_handler.py`

Sistema abrangente de tratamento de erros.

```python
from src.utils.error_handler import handle_chat_errors, ErrorCategory

@handle_chat_errors(
    category=ErrorCategory.DATABASE,
    user_message="Erro temporário no banco de dados"
)
async def database_operation():
    # Operação que pode falhar
    pass
```

### Frontend Components

#### 1. ChatLayout
**Localização**: `apps/frontend/src/components/chat/ChatLayout.tsx`

Componente principal que organiza a interface de chat.

```tsx
import { ChatLayout } from '@/components/chat/ChatLayout';

function ChatPage() {
  return <ChatLayout />;
}
```

#### 2. Chat Store
**Localização**: `apps/frontend/src/lib/chat-store.ts`

Estado global gerenciado com Zustand.

```tsx
import { useChatStore } from '@/lib/chat-store';

function ChatComponent() {
  const {
    threads,
    currentThread,
    messages,
    sendMessage,
    createThread
  } = useChatStore();
  
  // Uso do store...
}
```

## 📡 API Reference

### Base URL
```
http://localhost:8000/v1/chat
```

### Endpoints

#### Health Check
```http
GET /v1/chat/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-20T10:00:00Z",
  "database": {
    "status": "healthy",
    "connection": "active"
  }
}
```

#### Create Thread
```http
POST /v1/chat/threads
Content-Type: application/json
X-User-ID: user-123

{
  "title": "Nova Conversa",
  "client_id": "L2M",
  "sector": "cambio",
  "metadata": {}
}
```

**Response:**
```json
{
  "thread": {
    "id": "chat-20250120100000-abc123",
    "title": "Nova Conversa",
    "user_id": "user-123",
    "created_at": "2025-01-20T10:00:00Z",
    "is_active": true
  }
}
```

#### List Threads
```http
GET /v1/chat/threads?page=1&page_size=10
X-User-ID: user-123
```

#### Send Message
```http
POST /v1/chat/threads/{thread_id}/messages
Content-Type: application/json
X-User-ID: user-123

{
  "content": "Mostre as vendas dos últimos 3 meses",
  "stream": false,
  "metadata": {}
}
```

#### Stream Message
```http
GET /v1/chat/threads/{thread_id}/messages/stream?content=Mostre%20vendas
X-User-ID: user-123
Accept: text/event-stream
```

**SSE Events:**
```
event: status
data: {"event": "status", "status": "processing", "details": "Analisando pergunta..."}

event: token
data: {"event": "token", "content": "Com base nos dados...", "token_count": 5}

event: done
data: {"event": "done", "final_content": "Resposta completa", "tokens_used": 150}
```

## 🎨 Frontend Components

### Estrutura de Componentes

```
src/components/chat/
├── ChatLayout.tsx          # Layout principal
├── ChatSidebar.tsx         # Sidebar com threads
├── ChatMain.tsx            # Área principal de mensagens
├── ChatMessageList.tsx     # Lista de mensagens
├── ChatMessageItem.tsx     # Item individual de mensagem
├── ChatInput.tsx           # Input de mensagem
├── ChatWelcome.tsx         # Tela de boas-vindas
├── LoadingMessage.tsx      # Indicador de carregamento
└── index.ts               # Barrel exports
```

### Uso dos Componentes

```tsx
// Layout completo
import { ChatLayout } from '@/components/chat';

// Componentes individuais
import { 
  ChatSidebar, 
  ChatMain, 
  ChatInput 
} from '@/components/chat';

function CustomChatInterface() {
  return (
    <div className="flex h-screen">
      <ChatSidebar />
      <div className="flex-1">
        <ChatMain />
        <ChatInput />
      </div>
    </div>
  );
}
```

### Estado e Props

```tsx
// Props do ChatMessageItem
interface ChatMessageItemProps {
  message: ChatMessage;
  isStreaming?: boolean;
  onCopy?: () => void;
  onFeedback?: (type: string) => void;
}

// Estado do Chat Store
interface ChatState {
  threads: ChatThread[];
  currentThread: ChatThread | null;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  streamingMessage: StreamingMessage | null;
}
```

## 🗄️ Database Schema

### Tabelas Principais

#### conversation_threads
```sql
CREATE TABLE conversation_threads (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    client_id VARCHAR(50) DEFAULT 'L2M',
    sector VARCHAR(100) DEFAULT 'cambio',
    title VARCHAR(500) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_message_at TIMESTAMP
);
```

#### conversation_messages
```sql
CREATE TABLE conversation_messages (
    id VARCHAR(255) PRIMARY KEY,
    thread_id VARCHAR(255) REFERENCES conversation_threads(id),
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    tokens_used INTEGER DEFAULT 0,
    processing_time FLOAT DEFAULT 0.0,
    analysis_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Índices Recomendados

```sql
-- Performance indexes
CREATE INDEX idx_threads_user_id ON conversation_threads(user_id);
CREATE INDEX idx_threads_last_message ON conversation_threads(last_message_at DESC);
CREATE INDEX idx_messages_thread_id ON conversation_messages(thread_id);
CREATE INDEX idx_messages_created_at ON conversation_messages(created_at);
CREATE INDEX idx_messages_role ON conversation_messages(role);
```

## 🚀 Deployment

### Variáveis de Ambiente

```bash
# Database
DB_LEARNING_PASSWORD=your_supabase_password
DATABASE_URL=postgresql://user:pass@host:port/db

# Logging
LOG_LEVEL=INFO
LOG_DIR=logs
ENABLE_CONSOLE_LOG=true
ENABLE_FILE_LOG=true
ENABLE_JSON_LOG=true

# API
API_HOST=0.0.0.0
API_PORT=8000
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "src.interfaces.api:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Railway Deployment

```bash
# Deploy backend
railway login
railway link your-project-id
railway up

# Deploy frontend
cd apps/frontend
npm run build
railway up
```

## 🧪 Testing

### Executando Testes

```bash
# Backend tests
cd apps/backend
python -m pytest tests/ -v

# Frontend tests
cd apps/frontend
npm test

# Integration tests
python scripts/test_chat_integration.py
```

### Estrutura de Testes

```
apps/backend/tests/
├── test_gemini_chat_engine.py     # Testes do engine principal
├── test_simple_conversation_db.py # Testes do banco de dados
├── test_query_optimizer.py        # Testes de otimização
└── test_error_handler.py          # Testes de error handling

apps/frontend/src/components/chat/__tests__/
├── ChatLayout.test.tsx            # Testes do layout
├── ChatSidebar.test.tsx           # Testes da sidebar
└── ChatInput.test.tsx             # Testes do input

apps/frontend/src/lib/__tests__/
└── chat-store.test.ts             # Testes do store
```

## 📊 Monitoring & Logging

### Estrutura de Logs

```
logs/
├── datahero_chat.log              # Log geral da aplicação
├── errors.log                     # Log apenas de erros
├── performance.log                # Log de métricas de performance
└── archived/                      # Logs arquivados (rotação)
```

### Configuração de Logging

O sistema usa logging estruturado em JSON para facilitar análise e monitoramento:

```python
from src.utils.logging_config import setup_logging

# Configuração automática
setup_logging(
    log_level="INFO",
    log_dir="logs",
    enable_console=True,
    enable_file=True,
    enable_json=True
)
```

### Métricas Disponíveis

```python
# Cache statistics
cache_stats = query_optimizer.get_cache_stats()
# {
#   "cache_hit_rate": 75.5,
#   "total_queries": 1000,
#   "avg_query_time": 0.125
# }

# Error statistics
error_stats = error_handler.get_error_statistics()
# {
#   "total_errors": 25,
#   "most_common_errors": [...]
# }
```

### Logging Personalizado

```python
from src.utils.logging_config import get_chat_logger

# Logger com contexto
logger = get_chat_logger(
    'message_processing',
    user_id='user-123',
    thread_id='thread-456'
)

# Log de eventos específicos
logger.log_conversation_event(
    logging.INFO,
    'message_sent',
    'User sent message',
    extra={'message_length': len(content)}
)

# Log de performance
logger.log_performance(
    'query_execution',
    duration=1.25,
    success=True
)
```

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão com Banco
```
ERROR: Database connection failed
```

**Solução:**
- Verificar variável `DB_LEARNING_PASSWORD`
- Confirmar conectividade com Supabase
- Verificar logs em `logs/errors.log`

#### 2. Streaming Não Funciona
```
ERROR: SSE connection failed
```

**Solução:**
- Verificar headers `Accept: text/event-stream`
- Confirmar que o endpoint de streaming está ativo
- Verificar logs de performance

#### 3. Cache Não Está Funcionando
```
WARNING: Cache hit rate below 10%
```

**Solução:**
- Verificar configuração do QueryOptimizer
- Analisar padrões de query
- Ajustar TTL do cache

### Debug Mode

```bash
# Ativar debug logging
export LOG_LEVEL=DEBUG

# Executar com debug
python -m uvicorn src.interfaces.api:app --reload --log-level debug
```

### Health Checks

```bash
# Verificar saúde do sistema
curl http://localhost:8000/v1/chat/health

# Verificar métricas
curl http://localhost:8000/v1/chat/stats
```

---

## 📞 Suporte

Para suporte técnico ou dúvidas sobre a implementação:

1. **Logs**: Verificar `logs/errors.log` para erros específicos
2. **Métricas**: Usar endpoints de health e stats para diagnóstico
3. **Testes**: Executar suite de testes para validar funcionalidade
4. **Documentação**: Consultar esta documentação para referência completa

**Sistema desenvolvido para DataHero4 - Versão 1.0**
