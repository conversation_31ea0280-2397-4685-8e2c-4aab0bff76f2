# DataHero4 Conversational Chat - Technical Requirements Document

## 📋 Executive Summary

Transform DataHero4 from single-query interface to full conversational chat system while preserving all existing functionality and maintaining performance targets.

## 🎯 Functional Requirements

### FR1: Multi-turn Conversations
- **Description**: Support continuous conversation flow with context preservation
- **Current State**: Single-query only (messages cleared after each response)
- **Target**: Persistent conversation history with thread management
- **Priority**: High

### FR2: Context Preservation
- **Description**: Maintain conversation context across multiple messages
- **Current State**: No conversation memory (thread_id exists but not used conversationally)
- **Target**: Intelligent context window management with relevance scoring
- **Priority**: High

### FR3: Streaming Responses
- **Description**: Real-time response streaming for immediate user feedback
- **Current State**: No streaming (complete response only)
- **Target**: Server-Sent Events (SSE) with <2s time to first token
- **Priority**: High

### FR4: Thread Management
- **Description**: Create, manage, and persist conversation threads
- **Current State**: Basic thread_id for LangGraph checkpointing
- **Target**: Full thread lifecycle with user association and metadata
- **Priority**: Medium

### FR5: Feedback Integration
- **Description**: Maintain existing feedback system in conversational context
- **Current State**: Robust feedback system for single queries
- **Target**: Extend feedback to work with conversation messages
- **Priority**: Medium

## 🔧 Non-Functional Requirements

### NFR1: Performance
- **Time to First Token**: <2s (critical for user experience)
- **Complete Response**: <10s (maintain current performance)
- **Concurrent Users**: Support 100+ concurrent conversations
- **Context Switch Time**: <500ms between messages

### NFR2: Reliability
- **Uptime**: 99.9% availability
- **Error Recovery**: Graceful degradation from SSE to polling
- **Data Persistence**: No conversation data loss
- **Fallback Mode**: Single-query mode if chat fails

### NFR3: Scalability
- **Railway Constraints**: 512MB RAM, limited CPU
- **Context Window**: Max 8000 tokens per conversation
- **Thread Limit**: 1000 active threads per user
- **Message History**: 100 messages per thread (with compression)

### NFR4: Security
- **Thread Isolation**: Users can only access their own threads
- **Input Validation**: Prevent injection attacks
- **Rate Limiting**: Per-user conversation limits
- **Data Privacy**: Secure conversation storage

## 🏗️ Technical Architecture

### Backend Components

#### 1. Thread Manager Service
- **Purpose**: Manage conversation thread lifecycle
- **Integration**: Extends existing LangGraph checkpointer
- **Database**: New conversation_threads table
- **Features**: Create, load, persist, garbage collection

#### 2. Context Window Manager
- **Purpose**: Optimize conversation context for LLM
- **Integration**: Enhances existing context_enhancement_node
- **Algorithm**: Relevance scoring + token management
- **Features**: Smart pruning, conversation summarization

#### 3. Streaming Handler
- **Purpose**: Real-time response streaming
- **Technology**: FastAPI EventSourceResponse + SSE
- **Integration**: Wraps existing business_analysis_node
- **Features**: Token streaming, connection management, fallback

#### 4. Chat Engine Service
- **Purpose**: Orchestrate conversational flow
- **Integration**: Uses existing optimized_workflow
- **Features**: Message processing, thread coordination, streaming

### Frontend Components

#### 1. Chat Layout
- **Components**: ConversationSidebar, ChatThread, MessageList
- **Integration**: Extends existing Index.tsx layout
- **Features**: Responsive design, thread switching

#### 2. Message Components
- **Components**: MessageBubble, StreamingMessage
- **Integration**: Adapts existing ChatMessage component
- **Features**: Real-time updates, feedback integration

#### 3. State Management
- **Technology**: Zustand + React Query
- **Integration**: Extends existing useChatMessages hook
- **Features**: Optimistic updates, offline support

#### 4. SSE Client
- **Technology**: Native EventSource API
- **Integration**: New streaming service
- **Features**: Auto-reconnection, error handling

### Database Schema Extensions

#### 1. conversation_threads
```sql
CREATE TABLE conversation_threads (
    id VARCHAR(100) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    title VARCHAR(200),
    client_id VARCHAR(50) DEFAULT 'L2M',
    sector VARCHAR(50) DEFAULT 'cambio',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,
    message_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB
);
```

#### 2. conversation_messages
```sql
CREATE TABLE conversation_messages (
    id VARCHAR(100) PRIMARY KEY,
    thread_id VARCHAR(100) REFERENCES conversation_threads(id),
    role VARCHAR(20) NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    query_id VARCHAR(100), -- Link to query_history
    tokens_used INTEGER,
    processing_time REAL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);
```

#### 3. thread_context_windows
```sql
CREATE TABLE thread_context_windows (
    id VARCHAR(100) PRIMARY KEY,
    thread_id VARCHAR(100) REFERENCES conversation_threads(id),
    message_ids TEXT[], -- Array of message IDs in context
    total_tokens INTEGER,
    relevance_scores JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### API Endpoints

#### Chat Management
- `POST /v1/chat/threads` - Create new conversation thread
- `GET /v1/chat/threads` - List user's threads
- `GET /v1/chat/threads/{id}` - Get thread details
- `DELETE /v1/chat/threads/{id}` - Delete thread

#### Message Handling
- `POST /v1/chat/threads/{id}/messages` - Send message
- `GET /v1/chat/threads/{id}/messages` - Get message history
- `GET /v1/chat/threads/{id}/messages/{message_id}/stream` - SSE streaming

#### Feedback Integration
- `POST /v1/chat/threads/{id}/messages/{message_id}/feedback` - Submit feedback
- `POST /v1/chat/threads/{id}/messages/{message_id}/reprocess` - Reprocess with feedback

## 🔄 Integration Strategy

### Preserve Existing Functionality
- ✅ All current API endpoints (/ask, /feedback, etc.)
- ✅ Existing LangGraph workflow and nodes
- ✅ Current database tables and models
- ✅ Frontend components and hooks
- ✅ CLI interface and functionality

### Extend Gradually
- 🔄 DataHeroState with conversation fields
- 🔄 New chat endpoints alongside existing ones
- 🔄 Enhanced frontend with chat layout
- 🔄 Database schema additions (no modifications)

### Migration Path
1. **Phase 1**: Backend infrastructure (Thread Manager, Streaming)
2. **Phase 2**: Database schema extensions
3. **Phase 3**: API endpoints implementation
4. **Phase 4**: Frontend chat interface
5. **Phase 5**: Integration and testing
6. **Phase 6**: Production deployment with feature flags

## 📊 Success Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| Time to First Token | <2s | Backend latency monitoring |
| Streaming Speed | >30 tokens/s | Frontend render performance |
| Context Relevance | >85% | User feedback + A/B tests |
| Thread Completion Rate | >70% | Analytics de conversas |
| User Satisfaction | >4.5/5 | In-app feedback |
| System Uptime | >99.9% | Infrastructure monitoring |

## 🚨 Risk Mitigation

### High Risks
1. **Railway Resource Constraints**
   - Mitigation: Connection pooling, context optimization
2. **Streaming Performance**
   - Mitigation: SSE fallback to polling, token chunking
3. **Context Window Management**
   - Mitigation: Smart pruning, conversation summarization

### Medium Risks
1. **Database Migration**
   - Mitigation: Additive schema changes, rollback procedures
2. **Frontend State Complexity**
   - Mitigation: Proven patterns (Zustand + React Query)

## 🎯 Implementation Timeline

- **Week 1-2**: Backend infrastructure development
- **Week 3**: Database schema and API endpoints
- **Week 4-5**: Frontend chat interface
- **Week 6**: Integration and testing
- **Week 7-8**: Production deployment and monitoring

## ✅ Acceptance Criteria

### Backend
- [ ] Thread Manager creates and manages conversation threads
- [ ] Context Window Manager optimizes conversation context
- [ ] Streaming Handler provides real-time SSE responses
- [ ] Chat Engine orchestrates conversational flow
- [ ] All existing functionality preserved

### Frontend
- [ ] Chat layout with conversation sidebar
- [ ] Real-time message streaming
- [ ] Thread management UI
- [ ] Feedback system integrated
- [ ] Optimistic updates and offline support

### Integration
- [ ] End-to-end conversation flow
- [ ] Performance targets met (<2s TTFT)
- [ ] Error handling and fallbacks
- [ ] Production deployment successful
- [ ] User acceptance testing passed

This document serves as the technical foundation for implementing conversational chat in DataHero4 while maintaining system reliability and performance.
