# DataHero4 API Documentation

## 📋 Visão Geral

A API do DataHero4 é construída com FastAPI e fornece endpoints para interação com o sistema multi-agentes de análise de dados. A API utiliza o workflow otimizado com LangGraph e suporte a Nível 2 para máxima performance.

## 🚀 Base URL

- **Desenvolvimento**: `http://localhost:8000`
- **Produção**: `https://your-backend-domain.railway.app`

## 📚 Documentação Interativa

- **Swagger UI**: `/docs`
- **ReDoc**: `/redoc`
- **OpenAPI Schema**: `/openapi.json`

## 🔐 Autenticação

Atualmente a API não requer autenticação, mas está preparada para implementação futura de JWT/OAuth.

## 📡 Endpoints Principais

### 🏥 Health Check

#### `GET /health`

Verifica o status de saúde da aplicação e configurações.

**Resposta:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-16T10:30:00Z",
  "version": "1.0.0",
  "optimization_status": "active",
  "workflow_type": "optimized_parallel",
  "level2_enabled": true,
  "database": {
    "postgresql": "healthy"
  }
}
```

### 💬 Chat/Perguntas

#### `POST /ask`

Processa perguntas em linguagem natural e retorna análises completas.

**Request Body:**
```json
{
  "question": "Qual foi o volume total de vendas em janeiro?",
  "client_id": "L2M",
  "sector": "cambio",
  "thread_id": "optional-thread-id"
}
```

**Response:**
```json
{
  "result": {
    "question": "Qual foi o volume total de vendas em janeiro?",
    "sql_query": "SELECT SUM(valor_me) FROM boleta WHERE EXTRACT(MONTH FROM data_operacao) = 1",
    "sql_valid": true,
    "validation_errors": [],
    "query_result": [{"sum": 1500000.50}],
    "business_analysis": {
      "summary": "Análise do volume de vendas...",
      "key_insights": ["Insight 1", "Insight 2"],
      "recommendations": ["Recomendação 1"]
    },
    "insights": ["Volume total foi de R$ 1.5M", "Crescimento de 15% vs dezembro"],
    "suggestions": ["Qual foi o crescimento percentual?", "Quais os maiores clientes?"],
    "frontend_display": {
      "charts": [],
      "tables": [],
      "kpis": []
    },
    "execution_time": 3.2,
    "cache_hit": true,
    "confidence_score": 0.95
  }
}
```

### 📝 Feedback

#### `POST /feedback/simple`

Submete feedback simples sobre uma resposta.

#### `POST /feedback/reprocess`

Submete feedback e reprocessa automaticamente a consulta com correções.

### 📊 Métricas

#### `GET /metrics`

Retorna métricas gerais do sistema.

#### `GET /metrics/learning`

Retorna métricas específicas do sistema de aprendizado.

## ⚠️ Códigos de Erro

| Código | Descrição |
|--------|-----------|
| 400 | Bad Request - Parâmetros inválidos |
| 422 | Validation Error - Dados mal formatados |
| 500 | Internal Server Error - Erro interno |
| 503 | Service Unavailable - Serviço temporariamente indisponível |

## 📈 Rate Limiting

- **Burst**: 10 requests/segundo
- **Minute**: 60 requests/minuto  
- **Hour**: 1000 requests/hora

## 🧪 Exemplos de Uso

### cURL

```bash
# Fazer uma pergunta
curl -X POST "http://localhost:8000/ask" \
  -H "Content-Type: application/json" \
  -d '{"question": "Quantas operações tivemos hoje?"}'

# Verificar saúde
curl "http://localhost:8000/health"
```

### JavaScript/TypeScript

```typescript
// Cliente API
const apiClient = {
  baseURL: 'http://localhost:8000',
  
  async ask(question: string, clientId = 'L2M', sector = 'cambio') {
    const response = await fetch(`${this.baseURL}/ask`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ question, client_id: clientId, sector })
    });
    return response.json();
  }
};
```

## 🚀 Próximas Funcionalidades

- [ ] Autenticação JWT
- [ ] WebSocket para chat em tempo real
- [ ] Upload de arquivos para análise
- [ ] Exportação de relatórios
- [ ] API de administração
- [ ] Webhooks para integrações
