# 🛡️ DEPLOYMENT RULES & BEST PRACTICES

## 📋 CRITICAL RULES FOR RAILWAY DEPLOYMENT

### 🚨 NEVER DO THIS:
- ❌ Duplicate dependencies between Poetry groups
- ❌ Use `--only main` in nixpacks.toml (group doesn't exist)
- ❌ Put pytest in main dependencies (dev only)
- ❌ Create optional groups without proper configuration
- ❌ Commit pyproject.toml changes without local validation

### ✅ ALWAYS DO THIS:
- ✅ Use `--no-dev` instead of `--only main`
- ✅ Run `poetry check` before committing
- ✅ Test `poetry install --no-dev` locally
- ✅ Keep test dependencies in dev group only
- ✅ Validate nixpacks.toml commands work locally

## 🔧 PYPROJECT.TOML STRUCTURE

```toml
[tool.poetry.dependencies]
# Production dependencies ONLY
python = "^3.10"
fastapi = "^0.115.12"
# NO pytest here!

[tool.poetry.group.dev.dependencies]
# Development dependencies ONLY
pytest = "^8.3.5"
pytest-cov = "^6.1.1"
# NO duplication with main!
```

## 🔧 NIXPACKS.TOML CONFIGURATION

```toml
[phases.install]
# ✅ CORRECT:
cmds = ["poetry install --no-dev --no-interaction --no-ansi"]

# ❌ WRONG:
# cmds = ["poetry install --only main --no-interaction --no-ansi"]
```

## 📝 PRE-COMMIT CHECKLIST

Before committing changes to `pyproject.toml` or `nixpacks.toml`:

1. **Validate Poetry configuration:**
   ```bash
   poetry check
   ```

2. **Test deployment command:**
   ```bash
   poetry install --no-dev
   ```

3. **Check for duplicates:**
   ```bash
   poetry show --tree | grep -E "(pytest|duplicate-package)"
   ```

4. **Verify nixpacks command works:**
   ```bash
   # Test the exact command from nixpacks.toml
   poetry install --no-dev --no-interaction --no-ansi
   ```

## 🎯 COMMON MISTAKES & SOLUTIONS

| Problem | Cause | Solution |
|---------|-------|----------|
| `exit code: 1` on poetry install | Duplicate dependencies | Remove duplicates, keep in appropriate groups |
| `--only main` fails | Group doesn't exist | Use `--no-dev` instead |
| Build succeeds but runtime fails | Missing production deps | Move required deps from dev to main |
| Slow builds | Too many dependencies | Use dependency groups properly |

## 🔍 DEBUGGING DEPLOYMENT ISSUES

1. **Check Railway logs:**
   ```bash
   railway logs --deployment
   ```

2. **Validate locally:**
   ```bash
   poetry install --no-dev
   poetry run python -c "import sys; print('OK')"
   ```

3. **Test exact nixpacks command:**
   ```bash
   # Copy command from nixpacks.toml and test
   ```

## 📊 HISTORICAL ISSUES RESOLVED

- **June 16, 2025**: Fixed duplicate pytest dependencies causing `--only main` to fail
- **Lesson**: Always validate Poetry configuration before deployment
- **Root cause**: Gradual configuration drift across multiple commits

## 🎯 PREVENTION STRATEGY

1. **Automated validation** in development workflow
2. **Consistent Poetry group usage** across all projects
3. **Local testing** of deployment commands before commit
4. **Documentation** of working configurations for reference

---
**Remember**: Railway deployment failures are usually Poetry configuration issues, not code problems!
