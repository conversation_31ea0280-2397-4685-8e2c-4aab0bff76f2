# 🐚 Terminal ZSH - DataHero4

Configuração completa do terminal zsh para desenvolvimento otimizado no DataHero4.

## 🚀 Setup Rápido

```bash
# 1. Configurar zsh (uma vez só)
./scripts/setup-zsh.sh

# 2. Carregar configurações do projeto
source .zshrc.datahero

# 3. Verificar se está funcionando
dh-status
dh-help
```

## 📋 Scripts Disponíveis

### Scripts de Configuração
- `./scripts/setup-zsh.sh` - Configura o terminal zsh
- `./scripts/zsh-wrapper.sh` - Wrapper que garante uso do zsh
- `.zshrc.datahero` - Configurações e aliases do projeto

### Scripts de Desenvolvimento
- `./scripts/dev.sh` - Inicia ambiente de desenvolvimento (backend + frontend)
- `./scripts/test-all.sh` - Executa todos os testes
- `./scripts/validate-deps.sh` - Valida dependências Poetry

## 🎯 Aliases Úteis

Após carregar `.zshrc.datahero`, você tem acesso a:

### Comandos de Desenvolvimento
```bash
dh-dev          # Inicia ambiente de desenvolvimento
dh-test         # Executa todos os testes
dh-validate     # Valida dependências
dh-setup        # Configura terminal zsh
```

### Ferramentas
```bash
dh-turbo        # Executa comandos turbo
dh-npm          # Executa comandos npm
dh-poetry       # Executa comandos poetry
dh-git          # Executa comandos git
```

### Scripts Diretos
```bash
dev             # ./scripts/dev.sh
test-all        # ./scripts/test-all.sh
validate-deps   # ./scripts/validate-deps.sh
```

### Utilitários
```bash
dh-status       # Mostra status do projeto
dh-help         # Lista todos os comandos
```

## 🔧 Configurações Automáticas

O arquivo `.zshrc.datahero` configura automaticamente:

- **Variáveis de ambiente** para desenvolvimento
- **PATH** com node_modules/.bin
- **Cores** para output colorido
- **Aliases** para comandos frequentes
- **Funções** utilitárias

## 🛠️ Wrapper ZSH

O `zsh-wrapper.sh` garante que todos os comandos sejam executados no zsh:

```bash
# Uso do wrapper
./scripts/zsh-wrapper.sh dev      # Desenvolvimento
./scripts/zsh-wrapper.sh test     # Testes
./scripts/zsh-wrapper.sh validate # Validação
./scripts/zsh-wrapper.sh turbo    # Comandos turbo
./scripts/zsh-wrapper.sh npm      # Comandos npm
./scripts/zsh-wrapper.sh poetry   # Comandos poetry
./scripts/zsh-wrapper.sh git      # Comandos git
```

## 🎨 Output Colorido

Todos os scripts usam cores para melhor legibilidade:

- 🔵 **Azul**: Informações
- 🟢 **Verde**: Sucesso
- 🟡 **Amarelo**: Avisos
- 🔴 **Vermelho**: Erros

## 📊 Monitoramento

Use `dh-status` para verificar o estado do projeto:

```bash
$ dh-status
📊 Status do DataHero4
Shell: /bin/zsh
Diretório: /Users/<USER>/coding-projects/datahero4
Git Branch: main
Node.js: v22.13.0
Poetry: Poetry (version 2.1.2)
Turbo: 1.13.4
```

## 🔄 Workflow Recomendado

1. **Início da sessão:**
   ```bash
   cd /Users/<USER>/coding-projects/datahero4
   source .zshrc.datahero
   dh-status
   ```

2. **Desenvolvimento:**
   ```bash
   dh-dev          # Inicia servidores
   # Em outro terminal:
   dh-test         # Executa testes
   ```

3. **Validação antes de commit:**
   ```bash
   dh-validate     # Valida dependências
   dh-test         # Executa testes
   ```

## 🐛 Troubleshooting

### Problema: Comando não encontrado
```bash
# Solução: Recarregar configurações
source .zshrc.datahero
```

### Problema: Shell não é zsh
```bash
# Solução: Executar setup
./scripts/setup-zsh.sh
```

### Problema: Scripts não executam
```bash
# Solução: Verificar permissões
chmod +x scripts/*.sh
```

### Problema: Aliases não funcionam
```bash
# Solução: Verificar se carregou o arquivo
echo $SHELL
source .zshrc.datahero
```

## 📝 Personalização

Para personalizar ainda mais, edite `.zshrc.datahero`:

```bash
# Adicionar seus próprios aliases
alias meu-comando='./meu-script.sh'

# Adicionar funções personalizadas
minha-funcao() {
    echo "Minha função personalizada"
}
```

## 🎯 Próximos Passos

1. **Configure o terminal:** `./scripts/setup-zsh.sh`
2. **Carregue as configurações:** `source .zshrc.datahero`
3. **Teste os comandos:** `dh-help` e `dh-status`
4. **Inicie o desenvolvimento:** `dh-dev`

---

**💡 Dica:** Adicione `source .zshrc.datahero` ao seu `~/.zshrc` para carregar automaticamente as configurações do DataHero4 quando estiver no diretório do projeto.
