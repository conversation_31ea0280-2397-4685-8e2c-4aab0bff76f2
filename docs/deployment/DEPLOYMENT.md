# Guia de Deployment - DataHero4

Este guia detalha como fazer deploy do DataHero4 em diferentes ambientes, com foco na arquitetura de monorepo.

## 📋 Visão Geral

O DataHero4 é estruturado como um monorepo com dois serviços independentes:

| Serviço | Localização | Stack | Porta |
|---------|-------------|-------|-------|
| **Backend** | `apps/backend` | Python 3.10 + FastAPI + Poetry | 8000 |
| **Frontend** | `apps/frontend` | Node 20 + React + Vite | 3000 |

## 🚀 Railway (Recomendado)

### Configuração Inicial

1. **Criar Projeto no Railway**
   - Acesse [railway.app](https://railway.app)
   - Crie um novo projeto vazio
   - Conecte ao repositório GitHub

2. **Configurar Serviço Backend**
   ```yaml
   # Configurações do serviço backend
   Source: GitHub Repository
   Branch: main
   Root Directory: apps/backend
   Builder: Nixpacks (auto-detect)
   ```

3. **Configurar Serviço Frontend**
   ```yaml
   # Configurações do serviço frontend
   Source: GitHub Repository  
   Branch: main
   Root Directory: apps/frontend
   Builder: Nixpacks (auto-detect)
   ```

### Variáveis de Ambiente

#### Backend
```bash
# Banco de dados (Railway PostgreSQL)
DATABASE_URL=${{Postgres.DATABASE_URL}}
DB_LEARNING_PASSWORD=${{Postgres.POSTGRES_PASSWORD}}

# APIs LLM
TOGETHER_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# CORS
ALLOWED_ORIGINS=${{Frontend.RAILWAY_PUBLIC_DOMAIN}}

# Configurações opcionais
LOG_LEVEL=INFO
USE_OPTIMIZED_WORKFLOW=true
```

#### Frontend
```bash
# API Backend
VITE_API_BASE_URL=https://${{Backend.RAILWAY_PUBLIC_DOMAIN}}

# Configurações opcionais
NODE_ENV=production
```

### Processo de Deploy

1. **Push para main** - Deploy automático
2. **Verificar logs** nos serviços
3. **Testar endpoints**:
   - Backend: `https://backend-domain.railway.app/health`
   - Frontend: `https://frontend-domain.railway.app`

## 🐳 Docker (Alternativo)

### Backend Dockerfile

```dockerfile
# apps/backend/Dockerfile
FROM python:3.10-slim

WORKDIR /app

# Instalar Poetry
RUN pip install poetry

# Copiar arquivos de dependências
COPY pyproject.toml poetry.lock ./

# Instalar dependências
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev

# Copiar código
COPY . .

# Expor porta
EXPOSE 8000

# Comando de inicialização
CMD ["poetry", "run", "uvicorn", "src.interfaces.api:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Frontend Dockerfile

```dockerfile
# apps/frontend/Dockerfile
FROM node:20-alpine

WORKDIR /app

# Copiar package files
COPY package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código
COPY . .

# Build
RUN npm run build

# Expor porta
EXPOSE 3000

# Comando de inicialização
CMD ["npm", "run", "preview", "--", "--port", "3000"]
```

## 🔧 Configurações de Produção

### Backend

```python
# apps/backend/src/config/settings.py
class Settings(BaseSettings):
    # Produção
    DEBUG: bool = Field(default=False)
    LOG_LEVEL: str = Field(default="INFO")
    
    # CORS
    ALLOWED_ORIGINS: str = Field(default="*")
    
    # Performance
    USE_OPTIMIZED_WORKFLOW: bool = Field(default=True)
    CACHE_TTL: int = Field(default=3600)
```

### Frontend

```typescript
// apps/frontend/src/config/api.ts
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export const apiConfig = {
  baseURL: API_BASE_URL,
  timeout: 30000,
  retries: 3
};
```

## 📊 Monitoramento

### Health Checks

```bash
# Backend
curl https://your-backend.railway.app/health

# Frontend
curl https://your-frontend.railway.app/
```

### Logs

```bash
# Railway CLI
railway logs --service backend
railway logs --service frontend
```

## 🚨 Troubleshooting

### Problemas Comuns

#### Backend não inicia
```bash
# Verificar logs
railway logs --service backend

# Verificar variáveis
railway variables --service backend
```

#### Frontend não conecta ao backend
```bash
# Verificar CORS
curl -H "Origin: https://your-frontend.com" \
     -H "Access-Control-Request-Method: POST" \
     -X OPTIONS \
     https://your-backend.railway.app/ask
```

## 📈 Otimizações

### Performance

- **Backend**: Use cache Redis para queries frequentes
- **Frontend**: Implemente code splitting e lazy loading
- **Database**: Configure connection pooling
- **CDN**: Use CDN para assets estáticos

### Escalabilidade

- **Horizontal**: Multiple instances no Railway
- **Vertical**: Upgrade de recursos
- **Database**: Read replicas para queries pesadas
- **Cache**: Redis cluster para cache distribuído
