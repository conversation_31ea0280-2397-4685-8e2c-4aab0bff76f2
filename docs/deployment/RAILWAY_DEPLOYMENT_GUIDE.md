# 🚂 Railway Deployment Guide - DataHero4

> **<PERSON><PERSON><PERSON>**: Deploy do DataHero4 no Railway seguindo as melhores práticas identificadas na análise do codebase

## 📋 **Problemas Identificados e Soluções**

### **🔴 Problemas Críticos Corrigidos**

1. **Conflito de Pontos de Entrada**: Criado `webapp.py` único
2. **Configuração Railway Incompleta**: Atualizado `railway.toml` com melhores práticas
3. **Estrutura LangGraph Não Otimizada**: Adicionado `langgraph.json` padrão
4. **Falta de Dockerfile Otimizado**: Criado Dockerfile multi-stage

---

## 🚀 **Pré-requisitos**

### **1. Railway CLI**
```bash
# Instalar Railway CLI
npm install -g @railway/cli

# Fazer login
railway login
```

### **2. Dependências**
- **Python**: 3.10+
- **Poetry**: 1.7+
- **Node.js**: 18+
- **Docker**: (opcional, para build local)

---

## 🛠️ **Setup Inicial**

### **1. Configuração do Projeto**
```bash
# Clonar e configurar
git clone https://github.com/daniribeiroBR/datahero4.git
cd datahero4

# Executar script de correções
chmod +x scripts/fix-railway-issues.sh
./scripts/fix-railway-issues.sh
```

### **2. Configurar Railway**
```bash
# Criar projeto (se não existir)
railway init

# Conectar ao projeto existente
railway link

# Adicionar serviços necessários
railway add postgresql
railway add redis
```

---

## 🔧 **Configuração de Variáveis**

### **Backend (Obrigatórias)**
```bash
# APIs LLM
railway variables set TOGETHER_API_KEY=your_key_here --service backend
railway variables set ANTHROPIC_API_KEY=your_key_here --service backend
railway variables set OPENAI_API_KEY=your_key_here --service backend

# Configuração da aplicação
railway variables set USE_OPTIMIZED_WORKFLOW=true --service backend
railway variables set LOG_LEVEL=INFO --service backend
railway variables set PYTHONUNBUFFERED=1 --service backend
```

### **LangSmith (Opcional)**
```bash
railway variables set LANGCHAIN_TRACING_V2=true --service backend
railway variables set LANGCHAIN_PROJECT=datahero4-production --service backend
railway variables set LANGSMITH_API_KEY=your_key_here --service backend
```

---

## 🚀 **Deploy**

### **Método 1: Script Automatizado**
```bash
# Deploy completo automatizado
chmod +x scripts/deploy-railway.sh
./scripts/deploy-railway.sh
```

### **Método 2: Manual**
```bash
# Backend
railway up --service backend --detach

# Aguardar e testar
sleep 30
curl -f "$(railway domain --service backend)/health"

# Frontend
railway up --service frontend --detach
```

---

## 🏗️ **Arquitetura de Deploy**

### **Backend (LangGraph + FastAPI)**
- **Imagem**: `wolfi` (mais segura e leve)
- **Entrada**: `webapp:app` (seguindo padrão LangGraph)
- **Workers**: 2 (otimizado para Railway)
- **Health Check**: `/health` endpoint

### **Frontend (React + Vite)**
- **Build**: Otimizada com `npm ci --only=production`
- **Servidor**: Vite preview mode
- **CORS**: Configurado para URLs Railway

---

## 📊 **Monitoramento**

### **Verificar Status**
```bash
# Logs em tempo real
railway logs --service backend
railway logs --service frontend

# Status dos serviços
railway status

# Variáveis configuradas
railway variables --service backend
```

### **Health Checks**
```bash
# Backend
curl "$(railway domain --service backend)/health"

# Frontend  
curl "$(railway domain --service frontend)"

# API Docs
open "$(railway domain --service backend)/docs"
```

---

## 🔍 **Troubleshooting**

### **Problemas Comuns**

#### **1. "Attribute 'app' not found"**
```bash
# Verificar se webapp.py existe
ls apps/backend/webapp.py

# Testar importação local
cd apps/backend
poetry run python -c "from webapp import app; print('OK')"
```

#### **2. "Module not found"**
```bash
# Instalar dependências faltantes
cd apps/backend
poetry install --only=main
```

#### **3. "Database connection failed"**
```bash
# Verificar variáveis de banco
railway variables --service backend | grep -E "(DATABASE_URL|POSTGRES_URI)"

# Testar conexão
railway run --service backend python -c "import os; print(os.getenv('DATABASE_URL'))"
```

#### **4. "Redis connection failed"**
```bash
# Verificar Redis
railway variables --service backend | grep REDIS_URL

# Reconectar Redis se necessário
railway connect redis --service backend
```

---

## 📈 **Otimizações Implementadas**

### **Performance**
- ✅ **Multi-stage Dockerfile**: Reduz tamanho da imagem
- ✅ **Cache de dependências**: Poetry cache otimizado  
- ✅ **Workers configurados**: 2 workers para Railway
- ✅ **Health checks**: Monitoramento automático

### **Segurança**
- ✅ **Usuário não-root**: Container seguro
- ✅ **Variáveis secretas**: APIs protegidas
- ✅ **CORS configurado**: Acesso controlado
- ✅ **Imagem Wolfi**: Base mais segura

### **Confiabilidade**
- ✅ **Restart policy**: Recuperação automática
- ✅ **Health checks**: Detecção de falhas
- ✅ **Graceful shutdown**: Finalização limpa
- ✅ **Timeout configurado**: Evita travamentos

---

## 🎯 **Próximos Passos**

### **Após Deploy Bem-sucedido**
1. ⚡ **Configurar domínio customizado** (opcional)
2. 📊 **Setup monitoring** com Railway metrics
3. 🔄 **Configurar CI/CD** com GitHub Actions
4. 📈 **Monitoring de performance** com logs
5. 🔒 **Setup backup** do banco de dados

### **URLs Importantes**
- **Backend API**: `https://backend-production.up.railway.app`
- **Frontend**: `https://frontend-production.up.railway.app`  
- **API Docs**: `https://backend-production.up.railway.app/docs`
- **Health Check**: `https://backend-production.up.railway.app/health`

---

## 📞 **Suporte**

### **Logs Úteis**
```bash
# Logs detalhados
railway logs --service backend --tail 100

# Erros específicos
railway logs --service backend | grep -i error

# Performance
railway metrics --service backend
```

### **Comandos de Debug**
```bash
# Conectar ao container
railway shell --service backend

# Executar comandos remotos
railway run --service backend python --version
railway run --service backend poetry show
```

### **Contatos**
- **Railway Support**: [help.railway.app](https://help.railway.app)
- **DataHero Issues**: [GitHub Issues](https://github.com/daniribeiroBR/datahero4/issues)
- **LangGraph Docs**: [LangGraph Documentation](https://langchain-ai.github.io/langgraph/) 