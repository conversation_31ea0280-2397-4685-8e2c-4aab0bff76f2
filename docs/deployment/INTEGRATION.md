# Integração Frontend-Backend - DataHero4

Este documento detalha como o frontend React se integra com o backend FastAPI no DataHero4.

## 📋 Visão Geral

A integração entre frontend e backend é baseada em uma API REST com comunicação JSON, utilizando React Query para gerenciamento de estado e cache no frontend.

## 🔗 Arquitetura de Comunicação

```mermaid
graph LR
    A[React Frontend] -->|HTTP/JSON| B[FastAPI Backend]
    B -->|LangGraph| C[Multi-Agent System]
    B -->|SQL| D[PostgreSQL]
    B -->|Cache| E[Redis/Memory]
    A -->|WebSocket| F[Real-time Updates]
```

## 🛠️ Configuração da Integração

### Frontend (React)

#### Cliente API Base
```typescript
// src/lib/api.ts
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export const apiClient = {
  baseURL: API_BASE_URL,
  timeout: 30000,
  
  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
  }
};
```

#### React Query Setup
```typescript
// src/lib/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});
```

### Backend (FastAPI)

#### CORS Configuration
```python
# src/interfaces/api.py
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS.split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 📡 Endpoints e Hooks

### 1. Chat/Perguntas

#### Hook do Frontend
```typescript
// src/hooks/useChat.ts
export const useAskQuestion = () => {
  return useMutation({
    mutationFn: async (data: AskRequest) => {
      return apiClient.request<AskResponse>('/ask', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    },
    onSuccess: (data) => {
      // Atualizar cache de mensagens
      queryClient.setQueryData(['messages'], (old: Message[]) => [
        ...old,
        { id: Date.now(), ...data.result }
      ]);
    },
  });
};
```

#### Endpoint do Backend
```python
# src/interfaces/api.py
@app.post("/ask", response_model=AskResponse)
async def ask_question(request: AskRequest):
    """Processa pergunta e retorna análise completa."""
    result = await optimized_workflow.ainvoke({
        "question": request.question,
        "client_id": request.client_id,
        "sector": request.sector,
    })
    return AskResponse(result=result)
```

### 2. Sistema de Feedback

#### Hook do Frontend
```typescript
// src/hooks/useFeedback.ts
export const useFeedbackWithReprocess = () => {
  return useMutation({
    mutationFn: async (data: FeedbackReprocessRequest) => {
      return apiClient.request<AskResponse>('/feedback/reprocess', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    },
    onSuccess: (data, variables) => {
      if (variables.reprocess) {
        // Substituir mensagem original pela reprocessada
        queryClient.setQueryData(['messages'], (old: Message[]) =>
          old.map(msg => 
            msg.id === variables.feedback.query_id 
              ? { ...msg, ...data.result }
              : msg
          )
        );
      }
    },
  });
};
```

## 🎨 Componentes de Interface

### Chat Component
```typescript
// src/components/Chat.tsx
export const Chat: React.FC = () => {
  const { mutate: askQuestion, isLoading } = useAskQuestion();
  const { mutate: submitFeedback } = useFeedbackWithReprocess();
  
  const handleSubmit = (question: string) => {
    askQuestion({
      question,
      client_id: 'L2M',
      sector: 'cambio'
    });
  };
  
  const handleFeedback = (messageId: string, feedback: FeedbackData) => {
    submitFeedback({
      feedback: { ...feedback, query_id: messageId },
      reprocess: feedback.shouldReprocess
    });
  };
  
  return (
    <div className="chat-container">
      <MessageList onFeedback={handleFeedback} />
      <ChatInput onSubmit={handleSubmit} isLoading={isLoading} />
    </div>
  );
};
```

## 🔄 Fluxo de Dados

### 1. Pergunta do Usuário
```
Frontend → POST /ask → Backend → LangGraph → Database → Response → Frontend
```

### 2. Feedback com Reprocessamento
```
Frontend → POST /feedback/reprocess → Backend → Análise Semântica → Nova Query → Response → Frontend
```

### 3. Monitoramento
```
Frontend → GET /health (30s) → Backend → Status → Frontend
Frontend → GET /metrics (60s) → Backend → Métricas → Frontend
```

## 🛡️ Tratamento de Erros

### Frontend Error Boundary
```typescript
// src/components/ErrorBoundary.tsx
export const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundaryComponent
          onReset={reset}
          fallbackRender={({ error, resetErrorBoundary }) => (
            <ErrorFallback error={error} onRetry={resetErrorBoundary} />
          )}
        >
          {children}
        </ErrorBoundaryComponent>
      )}
    </QueryErrorResetBoundary>
  );
};
```

## 🔧 Configuração de Desenvolvimento

### Variáveis de Ambiente

#### Frontend (.env.local)
```bash
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws
VITE_ENABLE_DEVTOOLS=true
```

#### Backend (.env)
```bash
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true
LOG_LEVEL=DEBUG
```

## 📊 Monitoramento da Integração

### Métricas de Performance
- **Latência de API**: Tempo de resposta dos endpoints
- **Taxa de Erro**: Percentual de requests com falha
- **Cache Hit Rate**: Eficiência do cache do React Query
- **Uptime**: Disponibilidade dos serviços

## 🚀 Otimizações

### Performance
- **React Query**: Cache inteligente de dados
- **Code Splitting**: Carregamento lazy de componentes
- **Debouncing**: Redução de calls desnecessárias
- **Compression**: Gzip/Brotli no backend

### Escalabilidade
- **Connection Pooling**: Pool de conexões no backend
- **Rate Limiting**: Proteção contra abuse
- **CDN**: Assets estáticos via CDN
- **Load Balancing**: Múltiplas instâncias
