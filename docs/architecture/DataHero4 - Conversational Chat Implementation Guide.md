# DataHero4 - Conversational Chat Implementation Guide

## 🎯 Objetivo do Projeto

Transformar a interface atual do DataHero4 de **consultas únicas** (one-shot) para um **chat conversacional completo**, similar ao ChatGPT, <PERSON> ou Claude, onde:

- ✅ O usuário pode fazer múltiplas perguntas em sequência
- ✅ O sistema mantém contexto entre as mensagens
- ✅ As respostas aparecem progressivamente (streaming)
- ✅ Interface familiar e intuitiva (chat bubbles)
- ✅ Histórico de conversas persistente

### Estado Atual vs Estado Desejado

**Atual:**
```
User: "Qual volume USD hoje?"
System: [Resposta completa com cards]
[Fim da interação]
```

**Desejado:**
```
User: "Qual volume USD hoje?"
Assistant: "O volume USD hoje é $1.2M..."
User: "E comparando com ontem?"
Assistant: "Comparando com ontem, houve um crescimento de 15%..."
User: "Mostre um gráfico dessa evolução"
Assistant: [Gráfico + explicação contextual]
```

## 🏗️ Arquitetura de Alto Nível

### 1. Backend Modifications

```mermaid
graph TD
    A[Frontend Chat] -->|WebSocket/SSE| B[API Gateway]
    B --> C[Thread Manager]
    C --> D[Context Engine]
    D --> E[LangGraph Pipeline]
    E --> F[Gemini-2.0-flash]
    
    C --> G[(Thread Store)]
    D --> H[(Context Cache)]
    E --> I[(Vector Store)]
```

### 2. Componentes Principais

#### **Thread Manager** (Novo)
- Gerencia ciclo de vida das conversas
- Persiste histórico no PostgreSQL
- Associa threads a usuários
- Implementa garbage collection de threads antigas

#### **Context Engine** (Evolução do existente)
- Mantém janela deslizante de contexto (últimas N mensagens)
- Calcula relevância semântica entre mensagens
- Otimiza tokens enviados ao LLM
- Implementa "memory pruning" inteligente

#### **Streaming Handler** (Novo)
- Processa respostas do Gemini em chunks
- Implementa Server-Sent Events (SSE)
- Gerencia backpressure
- Fallback para polling se SSE falhar

## 🔧 Implementação Técnica

### 1. Integração com Gemini 2.0 Flash

```python
# Configuração do Gemini
from google.generativeai import GenerativeModel

class GeminiChatHandler:
    def __init__(self):
        self.model = GenerativeModel(
            model_name="gemini-2.0-flash-exp",
            generation_config={
                "temperature": 0.7,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 8192,
            }
        )
        
    async def stream_response(self, messages: List[Message], context: Dict):
        # Formatar histórico para Gemini
        chat = self.model.start_chat(history=self._format_history(messages))
        
        # Stream da resposta
        response = await chat.send_message_async(
            messages[-1].content,
            stream=True
        )
        
        async for chunk in response:
            yield chunk.text
```

### 2. Gestão de Estado no Backend

```python
# Thread State Management
class ThreadState:
    def __init__(self, thread_id: str):
        self.thread_id = thread_id
        self.messages: List[Message] = []
        self.context_window: ContextWindow = ContextWindow(max_tokens=8000)
        self.langgraph_state: Dict = {}
        
    def add_message(self, message: Message):
        self.messages.append(message)
        self.context_window.update(self.messages)
        
    def get_context_for_llm(self) -> List[Message]:
        # Retorna apenas mensagens relevantes dentro do limite de tokens
        return self.context_window.get_optimized_context()
```

### 3. API Endpoints Principais

```python
# FastAPI implementation
from fastapi import FastAPI, WebSocket
from sse_starlette.sse import EventSourceResponse

app = FastAPI()

@app.post("/v1/chat/threads")
async def create_thread(request: CreateThreadRequest):
    """Cria nova thread de conversa"""
    thread = ThreadManager.create_thread(
        user_id=request.user_id,
        metadata=request.metadata
    )
    return {"thread_id": thread.id}

@app.post("/v1/chat/threads/{thread_id}/messages")
async def send_message(thread_id: str, message: MessageRequest):
    """Envia mensagem e retorna message_id para streaming"""
    message_id = await ChatEngine.process_message(
        thread_id=thread_id,
        content=message.content
    )
    return {"message_id": message_id}

@app.get("/v1/chat/threads/{thread_id}/messages/{message_id}/stream")
async def stream_response(thread_id: str, message_id: str):
    """SSE endpoint para streaming da resposta"""
    async def event_generator():
        async for chunk in ChatEngine.stream_response(thread_id, message_id):
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "token",
                    "content": chunk
                })
            }
        
        yield {
            "event": "message",
            "data": json.dumps({"type": "done"})
        }
    
    return EventSourceResponse(event_generator())
```

### 4. Frontend Architecture (React)

```typescript
// Hook principal para chat
const useConversationalChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentStreamText, setCurrentStreamText] = useState("");
  
  const sendMessage = async (content: string) => {
    // 1. Adiciona mensagem do usuário otimisticamente
    const userMessage = { role: "user", content };
    setMessages(prev => [...prev, userMessage]);
    
    // 2. Envia para backend
    const { message_id } = await api.sendMessage(threadId, content);
    
    // 3. Inicia streaming
    const eventSource = new EventSource(
      `/v1/chat/threads/${threadId}/messages/${message_id}/stream`
    );
    
    let assistantMessage = "";
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === "token") {
        assistantMessage += data.content;
        setCurrentStreamText(assistantMessage);
      } else if (data.type === "done") {
        setMessages(prev => [...prev, {
          role: "assistant",
          content: assistantMessage
        }]);
        setCurrentStreamText("");
        eventSource.close();
      }
    };
  };
  
  return { messages, sendMessage, isStreaming, currentStreamText };
};
```

## 🚀 Estratégias de Otimização

### 1. **Latência Reduzida**

- **Pre-warming**: Iniciar conexão com Gemini enquanto usuário digita
- **Edge caching**: Cache de respostas frequentes no CDN
- **Pattern matching**: 70% das queries já têm SQL pronto (Nível 2)
- **Parallel processing**: Executar SQL enquanto gera insights

### 2. **Context Management**

```python
class ContextWindow:
    def __init__(self, max_tokens: int = 8000):
        self.max_tokens = max_tokens
        self.importance_scorer = ImportanceScorer()
        
    def get_optimized_context(self, messages: List[Message]) -> List[Message]:
        # 1. Score de importância para cada mensagem
        scored_messages = [
            (msg, self.importance_scorer.score(msg, messages))
            for msg in messages
        ]
        
        # 2. Ordenar por relevância
        scored_messages.sort(key=lambda x: x[1], reverse=True)
        
        # 3. Incluir até atingir limite de tokens
        context = []
        token_count = 0
        
        for msg, score in scored_messages:
            msg_tokens = count_tokens(msg.content)
            if token_count + msg_tokens <= self.max_tokens:
                context.append(msg)
                token_count += msg_tokens
                
        # 4. Reordenar cronologicamente
        return sorted(context, key=lambda m: m.timestamp)
```

### 3. **Experiência de Usuário**

- **Typing indicators**: Mostrar "DataHero está digitando..."
- **Partial responses**: Mostrar resposta enquanto processa SQL
- **Smart suggestions**: Chips com perguntas de follow-up
- **Error recovery**: Retry automático com degradação graciosa

## 📊 Métricas de Sucesso

| Métrica | Target | Como Medir |
|---------|--------|------------|
| Time to First Token | < 2s | Backend latency monitoring |
| Streaming Speed | > 30 tokens/s | Frontend render performance |
| Context Relevance | > 85% | User feedback + A/B tests |
| Thread Completion Rate | > 70% | Analytics de conversas |
| User Satisfaction | > 4.5/5 | In-app feedback |

## 🔄 Plano de Migração

### Fase 1: Backend Foundation (2 semanas)
- [ ] Implementar Thread Manager
- [ ] Adicionar suporte a streaming no LangGraph
- [ ] Integrar Gemini 2.0 Flash
- [ ] Criar endpoints SSE

### Fase 2: Frontend Development (2 semanas)
- [ ] Desenvolver componentes de chat
- [ ] Implementar streaming client
- [ ] Adicionar gestão de estado (Zustand)
- [ ] UI/UX similar ao ChatGPT

### Fase 3: Integration & Testing (1 semana)
- [ ] Conectar frontend com backend
- [ ] Testes de carga com múltiplos usuários
- [ ] Otimização de performance
- [ ] Beta testing com usuários internos

### Fase 4: Production Release (1 semana)
- [ ] Feature flags para rollout gradual
- [ ] Monitoring e alertas
- [ ] Documentação e treinamento
- [ ] Go-live com 10% dos usuários

## 🎯 Considerações Finais

1. **Gemini 2.0 Flash** foi escolhido por:
   - Suporte nativo a streaming
   - Contexto de 1M tokens
   - Latência ultra-baixa
   - Custo-benefício excelente

2. **Reutilização de código**:
   - 80% do backend atual será mantido
   - LangGraph nodes precisam apenas de ajustes
   - Sistema de feedback já está pronto

3. **Riscos mitigados**:
   - Fallback para modo single-query
   - Cache agressivo para reduzir custos
   - Rate limiting por usuário
   - Monitoramento de custos em tempo real

Este documento serve como norte para a implementação. Detalhes específicos serão refinados durante o desenvolvimento.