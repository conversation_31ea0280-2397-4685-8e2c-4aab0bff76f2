# State Management Architecture - DataHero4 Conversational Chat

## 🎯 Visão Geral

Este documento define a arquitetura de gerenciamento de estado para o sistema de chat conversacional do DataHero4, cobrindo:

- Estado no backend (LangGraph + PostgreSQL)
- Estado no frontend (Zustand + React Query)
- Sincronização entre camadas
- Estratégias de persistência e recuperação

## 🏗️ Arquitetura de Estado

```mermaid
graph TB
    subgraph "Frontend State"
        A[Zustand Store] --> B[Thread State]
        A --> C[Message State]
        A --> D[UI State]
        E[React Query Cache] --> F[API Responses]
    end
    
    subgraph "Backend State"
        G[LangGraph Checkpointer] --> H[Thread Memory]
        I[PostgreSQL] --> J[Persistent Storage]
        K[Redis Cache] --> L[Session State]
    end
    
    subgraph "Sync Layer"
        M[WebSocket/SSE] --> N[State Updates]
        O[REST API] --> P[State Queries]
    end
    
    A <--> M
    E <--> O
    G <--> I
    G <--> K
```

## 📦 Backend State Management

### 1. LangGraph State Configuration

```python
from typing import TypedDict, List, Dict, Optional
from langgraph.graph import StateGraph
from datetime import datetime

class ConversationState(TypedDict):
    """Estado principal da conversa no LangGraph"""
    thread_id: str
    messages: List[Dict]
    context_window: List[Dict]  # Mensagens relevantes para LLM
    current_query: str
    sql_history: List[Dict]  # Histórico de SQLs executados
    entities_extracted: Dict  # Entidades identificadas (moedas, datas, etc)
    user_preferences: Dict  # Preferências aprendidas do usuário
    error_count: int  # Contador de erros para circuit breaker
    metadata: Dict

class MessageState(TypedDict):
    """Estado de uma mensagem individual"""
    id: str
    thread_id: str
    role: str  # 'user' | 'assistant' | 'system'
    content: str
    timestamp: datetime
    tokens_used: int
    processing_time: float
    feedback: Optional[Dict]
    sql_generated: Optional[str]
    data_returned: Optional[List[Dict]]
    visualizations: Optional[List[Dict]]
```

### 2. Thread Lifecycle Management

```python
class ThreadManager:
    """Gerencia o ciclo de vida das threads de conversa"""
    
    def __init__(self, checkpointer, db_session):
        self.checkpointer = checkpointer
        self.db = db_session
        self.cache = RedisCache()
        
    async def create_thread(self, user_id: str, metadata: Dict) -> str:
        """Cria nova thread com estado inicial"""
        thread_id = generate_uuid()
        
        initial_state = ConversationState(
            thread_id=thread_id,
            messages=[],
            context_window=[],
            current_query="",
            sql_history=[],
            entities_extracted={},
            user_preferences=self._load_user_preferences(user_id),
            error_count=0,
            metadata={
                "user_id": user_id,
                "created_at": datetime.utcnow(),
                "client_id": metadata.get("client_id", "L2M"),
                "sector": metadata.get("sector", "cambio"),
                **metadata
            }
        )
        
        # Salvar no checkpointer do LangGraph
        await self.checkpointer.put(
            config={"configurable": {"thread_id": thread_id}},
            checkpoint={"state": initial_state}
        )
        
        # Salvar metadados no PostgreSQL
        await self.db.save_thread_metadata(thread_id, initial_state["metadata"])
        
        # Cache para acesso rápido
        await self.cache.set(f"thread:{thread_id}", initial_state, ttl=3600)
        
        return thread_id
        
    async def load_thread(self, thread_id: str) -> ConversationState:
        """Carrega thread do cache ou storage"""
        # 1. Tentar cache primeiro
        cached = await self.cache.get(f"thread:{thread_id}")
        if cached:
            return cached
            
        # 2. Carregar do checkpointer
        checkpoint = await self.checkpointer.get(
            config={"configurable": {"thread_id": thread_id}}
        )
        
        if not checkpoint:
            raise ThreadNotFoundError(thread_id)
            
        state = checkpoint["state"]
        
        # 3. Atualizar cache
        await self.cache.set(f"thread:{thread_id}", state, ttl=3600)
        
        return state
```

### 3. Context Window Management

```python
class ContextWindowManager:
    """Gerencia a janela de contexto para otimizar tokens"""
    
    MAX_TOKENS = 8000
    MIN_MESSAGES = 2  # Sempre manter última pergunta + resposta
    
    def __init__(self, token_counter):
        self.token_counter = token_counter
        self.relevance_scorer = RelevanceScorer()
        
    def optimize_context(self, messages: List[MessageState]) -> List[MessageState]:
        """Otimiza contexto baseado em relevância e limite de tokens"""
        if not messages:
            return []
            
        # Sempre incluir a última mensagem do usuário
        must_include = [messages[-1]] if messages[-1]["role"] == "user" else messages[-2:]
        remaining = messages[:-len(must_include)]
        
        # Calcular scores de relevância
        scored_messages = []
        for msg in remaining:
            score = self._calculate_relevance(msg, messages)
            scored_messages.append((score, msg))
            
        # Ordenar por relevância
        scored_messages.sort(reverse=True, key=lambda x: x[0])
        
        # Construir contexto respeitando limite de tokens
        context = must_include.copy()
        tokens_used = sum(msg["tokens_used"] for msg in must_include)
        
        for score, msg in scored_messages:
            if tokens_used + msg["tokens_used"] <= self.MAX_TOKENS:
                context.append(msg)
                tokens_used += msg["tokens_used"]
                
        # Reordenar cronologicamente
        return sorted(context, key=lambda m: m["timestamp"])
        
    def _calculate_relevance(self, message: MessageState, all_messages: List[MessageState]) -> float:
        """Calcula score de relevância de uma mensagem"""
        score = 0.0
        
        # Mensagens recentes são mais relevantes
        recency = 1.0 / (1 + (datetime.utcnow() - message["timestamp"]).seconds / 3600)
        score += recency * 0.3
        
        # Mensagens com SQL/dados são importantes
        if message.get("sql_generated") or message.get("data_returned"):
            score += 0.3
            
        # Mensagens com feedback positivo
        if message.get("feedback", {}).get("helpful"):
            score += 0.2
            
        # Similaridade semântica com última mensagem
        if all_messages:
            similarity = self.relevance_scorer.compute_similarity(
                message["content"], 
                all_messages[-1]["content"]
            )
            score += similarity * 0.2
            
        return score
```

### 4. Memory Persistence Strategy

```python
class MemoryPersistence:
    """Estratégia de persistência de memória"""
    
    def __init__(self, db_session):
        self.db = db_session
        
    async def persist_checkpoint(self, thread_id: str, state: ConversationState):
        """Persiste checkpoint no PostgreSQL"""
        # Compactar mensagens antigas
        if len(state["messages"]) > 100:
            state = await self._compact_old_messages(state)
            
        checkpoint_data = {
            "thread_id": thread_id,
            "state": json.dumps(state),
            "created_at": datetime.utcnow(),
            "checksum": calculate_checksum(state)
        }
        
        await self.db.save_checkpoint(checkpoint_data)
        
    async def _compact_old_messages(self, state: ConversationState) -> ConversationState:
        """Compacta mensagens antigas em resumos"""
        messages = state["messages"]
        
        # Manter últimas 50 mensagens intactas
        recent = messages[-50:]
        old = messages[:-50]
        
        # Gerar resumo das mensagens antigas
        summary = await self._generate_summary(old)
        
        # Criar nova lista com resumo + recentes
        compacted_messages = [{
            "role": "system",
            "content": f"Resumo de conversas anteriores: {summary}",
            "timestamp": old[0]["timestamp"],
            "is_summary": True
        }] + recent
        
        state["messages"] = compacted_messages
        return state
```

## 💻 Frontend State Management

### 1. Zustand Store Structure

```typescript
// stores/chatStore.ts
interface ChatStore {
  // Thread Management
  threads: Thread[];
  currentThreadId: string | null;
  
  // Message Management
  messages: Record<string, Message[]>; // threadId -> messages
  streamingMessage: StreamingMessage | null;
  
  // UI State
  isLoading: boolean;
  isSidebarOpen: boolean;
  selectedCard: string | null;
  
  // Actions
  createThread: (metadata?: ThreadMetadata) => Promise<string>;
  loadThread: (threadId: string) => Promise<void>;
  sendMessage: (content: string) => Promise<void>;
  updateStreamingMessage: (chunk: string) => void;
  finalizeStreamingMessage: () => void;
  
  // Context Management
  contextWindow: Message[];
  updateContextWindow: () => void;
}

const useChatStore = create<ChatStore>((set, get) => ({
  threads: [],
  currentThreadId: null,
  messages: {},
  streamingMessage: null,
  isLoading: false,
  isSidebarOpen: true,
  selectedCard: null,
  
  createThread: async (metadata) => {
    const { thread_id } = await api.createThread(metadata);
    
    set(state => ({
      threads: [...state.threads, { id: thread_id, ...metadata }],
      currentThreadId: thread_id,
      messages: { ...state.messages, [thread_id]: [] }
    }));
    
    return thread_id;
  },
  
  sendMessage: async (content) => {
    const { currentThreadId } = get();
    if (!currentThreadId) return;
    
    // Optimistic update
    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content,
      timestamp: new Date()
    };
    
    set(state => ({
      messages: {
        ...state.messages,
        [currentThreadId]: [...(state.messages[currentThreadId] || []), userMessage]
      },
      isLoading: true
    }));
    
    // Send to backend
    const { message_id } = await api.sendMessage(currentThreadId, content);
    
    // Initialize streaming
    set({ 
      streamingMessage: { 
        id: message_id, 
        content: '', 
        role: 'assistant' 
      } 
    });
    
    // Start SSE connection
    await streamManager.startStream(currentThreadId, message_id);
  },
  
  updateStreamingMessage: (chunk) => {
    set(state => ({
      streamingMessage: state.streamingMessage 
        ? { ...state.streamingMessage, content: state.streamingMessage.content + chunk }
        : null
    }));
  },
  
  updateContextWindow: () => {
    const { currentThreadId, messages } = get();
    if (!currentThreadId) return;
    
    const threadMessages = messages[currentThreadId] || [];
    const contextWindow = optimizeContextWindow(threadMessages);
    
    set({ contextWindow });
  }
}));
```

### 2. React Query Integration

```typescript
// hooks/useThreadQueries.ts
export const useThreadQueries = () => {
  const queryClient = useQueryClient();
  
  // Query para carregar threads
  const threadsQuery = useQuery({
    queryKey: ['threads'],
    queryFn: api.getThreads,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
  
  // Query para carregar mensagens de uma thread
  const useThreadMessages = (threadId: string) => {
    return useQuery({
      queryKey: ['threads', threadId, 'messages'],
      queryFn: () => api.getThreadMessages(threadId),
      enabled: !!threadId,
      // Atualizar store do Zustand quando dados chegarem
      onSuccess: (messages) => {
        useChatStore.setState(state => ({
          messages: {
            ...state.messages,
            [threadId]: messages
          }
        }));
      }
    });
  };
  
  // Mutation para enviar mensagem
  const sendMessageMutation = useMutation({
    mutationFn: ({ threadId, content }: { threadId: string; content: string }) => 
      api.sendMessage(threadId, content),
    onMutate: async ({ threadId, content }) => {
      // Optimistic update
      await queryClient.cancelQueries(['threads', threadId, 'messages']);
      
      const previousMessages = queryClient.getQueryData(['threads', threadId, 'messages']);
      
      queryClient.setQueryData(['threads', threadId, 'messages'], (old: Message[]) => [
        ...old,
        { id: 'temp', role: 'user', content, timestamp: new Date() }
      ]);
      
      return { previousMessages };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousMessages) {
        queryClient.setQueryData(
          ['threads', variables.threadId, 'messages'], 
          context.previousMessages
        );
      }
    }
  });
  
  return {
    threads: threadsQuery.data,
    isLoadingThreads: threadsQuery.isLoading,
    useThreadMessages,
    sendMessage: sendMessageMutation.mutate
  };
};
```

### 3. State Synchronization

```typescript
// services/stateSyncService.ts
class StateSyncService {
  private eventSource: EventSource | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  
  async syncThreadState(threadId: string) {
    const syncUrl = `/api/threads/${threadId}/sync`;
    
    this.eventSource = new EventSource(syncUrl);
    
    this.eventSource.onmessage = (event) => {
      const update = JSON.parse(event.data);
      this.handleStateUpdate(update);
    };
    
    this.eventSource.onerror = () => {
      this.handleConnectionError();
    };
  }
  
  private handleStateUpdate(update: StateUpdate) {
    switch (update.type) {
      case 'message_added':
        useChatStore.setState(state => ({
          messages: {
            ...state.messages,
            [update.threadId]: [
              ...(state.messages[update.threadId] || []),
              update.message
            ]
          }
        }));
        break;
        
      case 'context_updated':
        useChatStore.setState({ contextWindow: update.context });
        break;
        
      case 'thread_metadata_updated':
        useChatStore.setState(state => ({
          threads: state.threads.map(t => 
            t.id === update.threadId 
              ? { ...t, ...update.metadata }
              : t
          )
        }));
        break;
    }
  }
  
  private handleConnectionError() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      setTimeout(() => {
        console.log(`Reconnecting... Attempt ${this.reconnectAttempts}`);
        this.syncThreadState(useChatStore.getState().currentThreadId!);
      }, Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000));
    }
  }
}
```

### 4. Offline Support & Persistence

```typescript
// services/offlineManager.ts
class OfflineManager {
  private db: IDBDatabase;
  
  async initialize() {
    this.db = await this.openDatabase();
    this.setupEventListeners();
  }
  
  private async openDatabase(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('DataHeroChat', 1);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Store for threads
        if (!db.objectStoreNames.contains('threads')) {
          db.createObjectStore('threads', { keyPath: 'id' });
        }
        
        // Store for messages
        if (!db.objectStoreNames.contains('messages')) {
          const messageStore = db.createObjectStore('messages', { keyPath: 'id' });
          messageStore.createIndex('threadId', 'threadId');
        }
        
        // Store for pending actions
        if (!db.objectStoreNames.contains('pendingActions')) {
          db.createObjectStore('pendingActions', { keyPath: 'id', autoIncrement: true });
        }
      };
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
  
  async saveThreadLocally(thread: Thread) {
    const transaction = this.db.transaction(['threads'], 'readwrite');
    await transaction.objectStore('threads').put(thread);
  }
  
  async saveMessageLocally(message: Message) {
    const transaction = this.db.transaction(['messages'], 'readwrite');
    await transaction.objectStore('messages').put(message);
  }
  
  async queueAction(action: PendingAction) {
    const transaction = this.db.transaction(['pendingActions'], 'readwrite');
    await transaction.objectStore('pendingActions').add(action);
  }
  
  async syncPendingActions() {
    const transaction = this.db.transaction(['pendingActions'], 'readonly');
    const actions = await transaction.objectStore('pendingActions').getAll();
    
    for (const action of actions) {
      try {
        await this.executeAction(action);
        
        // Remove action after successful execution
        const deleteTransaction = this.db.transaction(['pendingActions'], 'readwrite');
        await deleteTransaction.objectStore('pendingActions').delete(action.id);
      } catch (error) {
        console.error('Failed to sync action:', action, error);
      }
    }
  }
}
```

## 🔄 Estado de Sincronização Entre Camadas

### Fluxo de Sincronização

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant LangGraph
    participant DB
    
    User->>Frontend: Envia mensagem
    Frontend->>Frontend: Optimistic update
    Frontend->>API: POST /messages
    API->>LangGraph: Process with context
    LangGraph->>DB: Checkpoint state
    LangGraph-->>API: Stream response
    API-->>Frontend: SSE updates
    Frontend->>Frontend: Update UI progressively
    
    Note over Frontend,DB: Estado sincronizado em todas as camadas
```

## 📊 Métricas e Monitoramento

### Métricas de Estado

```typescript
interface StateMetrics {
  // Performance
  averageContextSize: number;
  contextOptimizationTime: number;
  stateLoadTime: number;
  
  // Usage
  activeThreads: number;
  messagesPerThread: number;
  contextWindowHitRate: number;
  
  // Errors
  stateSyncFailures: number;
  checkpointFailures: number;
  contextOverflows: number;
}

// Coletar métricas
const collectStateMetrics = () => {
  const metrics: StateMetrics = {
    averageContextSize: calculateAverageContextSize(),
    contextOptimizationTime: measureOptimizationTime(),
    stateLoadTime: measureLoadTime(),
    // ... outras métricas
  };
  
  // Enviar para sistema de monitoramento
  sendToDatadog(metrics);
};
```

## 🎯 Best Practices

1. **Imutabilidade**: Sempre criar novos objetos ao atualizar estado
2. **Otimistic Updates**: Atualizar UI imediatamente, corrigir se necessário
3. **Graceful Degradation**: Funcionar offline com funcionalidade reduzida
4. **State Compression**: Compactar threads antigas para economizar espaço
5. **Security**: Nunca expor dados sensíveis no estado do frontend
6. **Monitoring**: Instrumentar todas as operações de estado

Este documento será atualizado conforme a implementação evolui.