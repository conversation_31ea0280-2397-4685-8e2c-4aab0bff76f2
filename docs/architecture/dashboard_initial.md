## FEATURE:

Conectar o **dashboard frontend existente** (100% pronto) com o **backend API DataHero4** (95% implementado) através de **3 endpoints específicos** que faltam na API existente.

**Contexto real descoberto:**
- ✅ **Backend FastAPI funcionando** - 17 endpoints já implementados
- ✅ **87 KPIs definidos** com fórmulas SQL prontas (`kpis-exchange-json.json`)
- ✅ **Cache hierárquico de 3 níveis** implementado e operacional
- ✅ **Frontend dashboard completo** com shadcn/ui + Recharts + Motion
- ❌ **3 endpoints faltando**: `/api/dashboard/kpis`, `/api/kpis/{id}/calculate`, `/api/dashboard`

**Funcionalidades a implementar:**
- Adicionar 3 endpoints específicos aos 17 existentes
- **Migrar 87 KPIs** do JSON para tabela PostgreSQL (preparação para expansão)
- Reutilizar infraestrutura de cache já implementada
- Conectar frontend aos dados reais (substituir dados mock)
- **Future-ready**: Estrutura preparada para admin interface e KPIs dinâmicos

## EXAMPLES:

**Backend API existente (preservar 100%):**
- `apps/backend/src/main.py` - FastAPI app com 17 endpoints funcionando
- `apps/backend/src/caching/` - Cache hierárquico 3 níveis já implementado
- `apps/backend/src/config/setores/cambio/kpis-exchange-json.json` - 87 KPIs definidos
- Sistema completo em produção no Railway

**Frontend pronto (apenas conectar):**
- `apps/frontend/src/hooks/useKpiData.ts` - Hook usando dados mock, precisa apontar para API real
- `apps/frontend/src/pages/Dashboard.tsx` - Interface 100% pronta
- `apps/frontend/src/lib/api.ts` - Interfaces TypeScript esperando endpoints específicos

**Infraestrutura existente para reutilizar:**
- Cache hierárquico: L1 (Memory) + L2 (Redis) + L3 (PostgreSQL)
- Sistema de KPIs: 87 definições com fórmulas SQL validadas
- Pipeline LangGraph: Sistema completo de processamento de queries
- Database connection: PostgreSQL configurado e funcionando

## DOCUMENTATION:

**APIs e sistemas existentes:**
- FastAPI app: 17 endpoints já funcionando em produção
- Cache system: Documentação em `apps/backend/src/caching/`
- KPI system: Configurações em `src/config/setores/cambio/`
- Performance atual: Cache hit 85%, response time 1.5s-3s

**Endpoints existentes no backend (17 total):**
```python
POST /ask                    # Pipeline completo DataHero
GET  /health                # Health check com verificação
POST /chat/send             # Chat em tempo real
GET  /chat/history/{id}     # Histórico de conversas
POST /feedback/correction   # Sistema de feedback
GET  /debug/context/metrics # Debug e monitoramento
# ... +11 outros endpoints funcionais
```

**Endpoints faltando (apenas 3):**
```python
GET  /api/dashboard/kpis          # Lista KPIs calculados
GET  /api/kpis/{kpi_id}/calculate # Calcula KPI específico  
GET  /api/dashboard              # Dashboard completo
```

## OTHER CONSIDERATIONS:

**Simplicidade extrema (3-4 dias de trabalho):**

1. **NÃO criar nova infraestrutura** - Reutilizar os 17 endpoints e cache existentes
2. **NÃO implementar feature flags** - Sistema já maduro, apenas adicionar endpoints
3. **NÃO criar sistema de KPIs** - 87 KPIs já definidos com SQL, apenas executar
4. **NÃO tocar no frontend** - Interface 100% pronta, apenas mudar `useKpiData.ts`

**Implementação estratégica (preparação para expansão):**
```python
# 1. Criar tabela PostgreSQL para KPIs
CREATE TABLE kpis (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    formula_sql TEXT NOT NULL,
    unit VARCHAR,
    category VARCHAR,
    sector VARCHAR DEFAULT 'cambio'
);

# 2. Migrar 87 KPIs: JSON → PostgreSQL

# 3. Adicionar aos endpoints existentes em apps/backend/src/main.py
@app.get("/api/dashboard/kpis")
async def get_dashboard_kpis():
    # Query tabela PostgreSQL
    # Usar cache hierárquico existente
    # Executar SQL das fórmulas
    # Retornar formato esperado pelo frontend
```

**Reutilização total da infraestrutura:**
- **Cache strategy**: Usar `hierarchical_cache` existente (L1+L2+L3)
- **KPI calculation**: Usar definições de `kpis-exchange-json.json`
- **Database queries**: Reutilizar padrões de conexão PostgreSQL existentes
- **Error handling**: Seguir padrões dos 17 endpoints existentes
- **CORS e middleware**: Já configurados na FastAPI app existente

**Frontend connection (mudança mínima):**
```typescript
// apps/frontend/src/hooks/useKpiData.ts
// ANTES: const mockKpis = [/* dados fixos */];
// DEPOIS: const response = await fetch('/api/dashboard/kpis');
```

**Aproveitamento da arquitetura madura:**
- Sistema de 95% da infraestrutura já implementada
- Performance otimizada: 85% cache hit, 1.5s response time  
- 17 endpoints já em produção estável
- Pipeline LangGraph totalmente operacional
- Cache hierárquico de 3 níveis funcionando

**Trabalho real necessário:**
- Dia 1: Criar tabela KPIs e migrar 87 definições JSON → PostgreSQL
- Dia 2: Adicionar 3 endpoints aos 17 existentes (usando tabela)
- Dia 3: Conectar frontend aos endpoints reais
- Dia 4: Testes de integração e deploy
- Dia 5: Validação e monitoramento

**Redução de esforço:** 95% menos trabalho que estimativa inicial