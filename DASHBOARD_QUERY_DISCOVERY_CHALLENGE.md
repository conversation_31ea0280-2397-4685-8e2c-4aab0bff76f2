# 🔍 Dashboard Query Discovery Challenge

## 📋 Overview

Durante a implementação da integração do dashboard com KPIs em tempo real, descobrimos uma discrepância crítica entre as queries SQL hardcoded e a estrutura real da base de dados de produção.

## ❌ Problema Identificado

### Suposições Iniciais (Incorretas)
Baseei a implementação em suposições sobre a estrutura da base de dados:

```sql
-- ❌ Queries hardcoded baseadas em suposições
FROM cambio 
WHERE cliente_id = 'L2M'
SELECT valor_moeda_estrangeira, taxa_aplicada
```

### Realidade da Base de Dados
Após conexão com a base real (`l2m_prod`), descobri:

```sql
-- ✅ Estrutura real da base
FROM boleta 
WHERE id_cliente = 814  -- IDs numéricos, não strings
SELECT valor_me, taxa_cambio  -- Nomes de colunas diferentes
```

## 🎯 Descobertas da Análise

### Estrutura Real da Base
- **Tabela Principal**: `boleta` (não `cambio`)
- **Total de Registros**: 23.684 operações
- **Período Ativo**: 2024-09 até 2025-02-14
- **Volume Mensal**: ~70-90 milhões USD

### Mapeamento de Colunas
| Hardcode (Errado) | Realidade | Observações |
|-------------------|-----------|-------------|
| `cambio` | `boleta` | Tabela principal |
| `cliente_id = 'L2M'` | `id_cliente = 814` | IDs numéricos |
| `valor_moeda_estrangeira` | `valor_me` | Nome simplificado |
| `taxa_aplicada` | `taxa_cambio` | Nome padronizado |
| `codigo_moeda` | `id_moeda` | Foreign key |
| `data_operacao` | `data_operacao` | ✅ Correto |

### Dados de Exemplo
```
ID      | Data       | Valor ME    | Tipo    | Taxa     | Cliente
--------|------------|-------------|---------|----------|--------
25142   | 2025-02-14 | 42,037.00   | VENDA   | 5.8900   | 4586
25129   | 2025-02-14 | 4,431.00    | COMPRA  | 5.6800   | 538
25137   | 2025-02-14 | 89,980.00   | COMPRA  | 3.9700   | 2809
```

### Clientes Mais Ativos
- Cliente 814: 403 operações
- Cliente 134: 264 operações  
- Cliente 4586: 195 operações
- Cliente 4446: 171 operações

## 🛠️ Dois Caminhos para Resolver

### 📍 Caminho 1: Análise Manual via Python
**Abordagem**: Usar psycopg2 diretamente no terminal

**Vantagens**:
- Controle total sobre as queries
- Análise exploratória flexível
- Debugging detalhado

**Processo**:
1. Conectar via psycopg2 com credenciais existentes
2. Explorar schema completo da tabela `boleta`
3. Analisar relacionamentos com tabelas auxiliares (`moeda`, `banco_operacao`)
4. Criar queries teste para cada um dos 34 KPIs
5. Validar resultados com dados conhecidos

**Exemplo de Implementação**:
```python
# Conectar e explorar
conn = psycopg2.connect(DATABASE_URL)
cursor = conn.cursor()

# Testar KPI Volume Total
cursor.execute("""
SELECT SUM(valor_me) as total_volume
FROM boleta 
WHERE data_operacao >= '2025-02-01'
  AND id_cliente = 814
""")
```

### 📍 Caminho 2: MCP PostgreSQL na IDE
**Abordagem**: Usar MCP PostgreSQL para análise interativa

**Vantagens**:
- Interface integrada na IDE
- Queries interativas com autocomplete
- Visualização de resultados estruturada
- Debugging visual

**Processo**:
1. Configurar MCP PostgreSQL com as credenciais da base
2. Explorar schema visualmente
3. Executar queries teste diretamente na IDE
4. Refinar KPIs baseado nos resultados
5. Gerar queries finais otimizadas

**Benefícios do MCP**:
- Autocomplete de tabelas e colunas
- Visualização de relacionamentos
- Histórico de queries
- Export de resultados

## 🎯 KPIs que Precisam ser Reescritos

### Volume Metrics (4 KPIs)
- ❌ `total_volume`: FROM cambio → FROM boleta
- ❌ `volume_by_currency`: JOIN com moeda
- ❌ `average_ticket`: valor_moeda_estrangeira → valor_me
- ❌ `growth_percentage`: Comparação temporal

### Performance Metrics (6 KPIs)
- ❌ `average_spread`: taxa_aplicada → taxa_cambio
- ❌ `spread_trend`: Análise temporal
- ❌ `conversion_rate`: Lógica de conversão
- ❌ `market_share`: Comparação com mercado
- ❌ `profit_margin`: Cálculo de margem
- ❌ `roi`: Return on Investment

### Operational Metrics (8 KPIs)
- ❌ `transaction_count`: COUNT(*) FROM boleta
- ❌ `processing_time`: Tempo de processamento
- ❌ `approval_rate`: Taxa de aprovação
- ❌ `rejection_rate`: Taxa de rejeição
- ❌ `average_processing_days`: Dias médios
- ❌ `operational_efficiency`: Eficiência operacional
- ❌ `throughput`: Volume processado
- ❌ `capacity_utilization`: Utilização de capacidade

### Risk Metrics (5 KPIs)
- ❌ `var`: Value at Risk
- ❌ `exposure_concentration`: Concentração de exposição
- ❌ `currency_risk`: Risco cambial
- ❌ `counterparty_risk`: Risco de contraparte
- ❌ `compliance_score`: Score de compliance

### Quality Metrics (4 KPIs)
- ❌ `error_rate`: Taxa de erro
- ❌ `accuracy_rate`: Taxa de precisão
- ❌ `sla_compliance`: Cumprimento de SLA
- ❌ `customer_satisfaction`: Satisfação do cliente

### Efficiency Metrics (4 KPIs)
- ❌ `cost_per_transaction`: Custo por transação
- ❌ `automation_rate`: Taxa de automação
- ❌ `productivity_index`: Índice de produtividade
- ❌ `resource_utilization`: Utilização de recursos

### Growth Metrics (3 KPIs)
- ❌ `month_over_month`: Crescimento MoM
- ❌ `year_over_year`: Crescimento YoY
- ❌ `customer_growth`: Crescimento de clientes

## 🚨 Questões Críticas a Resolver

### 1. Identificação do Cliente
**Problema**: As queries usam `cliente_id = 'L2M'` mas a base usa IDs numéricos.

**Possíveis Soluções**:
- Mapear "L2M" para ID numérico específico
- Usar cliente mais ativo (814) para testes
- Criar lógica para múltiplos clientes

### 2. Estrutura de Moedas
**Problema**: Relacionamento com tabela `moeda` é via FK `id_moeda`.

**Necessário**:
- JOIN correto para obter código da moeda (USD, BRL)
- Mapeamento de símbolos e descrições

### 3. Cálculos de Spread
**Problema**: Como calcular spread sem `taxa_referencia`?

**Investigar**:
- Se existe taxa de referência em outra tabela
- Como calcular spread baseado em `taxa_cambio`
- Se existe taxa base ou benchmark

### 4. Filtros Temporais
**Problema**: Lógica de períodos (current_month, YTD) precisa ser validada.

**Validar**:
- Se `data_operacao` é sempre preenchida
- Fuso horário dos dados
- Lógica de fim de mês/ano

## ⚡ Recomendação

**Sugestão**: Usar **Caminho 2 (MCP PostgreSQL)** para análise inicial, depois implementar via **Caminho 1** para execução.

**Razão**: 
- MCP oferece interface mais amigável para descoberta
- Python oferece mais controle para implementação final
- Combinação dos dois maximiza eficiência

## 🎯 Próximos Passos

1. **Escolher abordagem** (MCP vs Python direto)
2. **Mapear cliente L2M** para ID numérico
3. **Reescrever queries** uma por uma
4. **Testar cada KPI** individualmente
5. **Validar resultados** com dados conhecidos
6. **Atualizar configuração JSON** com queries corretas
7. **Testar dashboard** completo com dados reais

## 📊 Impacto

**Estimativa de Trabalho**:
- ⏱️ **Descoberta via MCP**: 2-3 horas
- 🔧 **Reescrita de queries**: 4-6 horas  
- 🧪 **Testes e validação**: 2-3 horas
- 📱 **Integração final**: 1-2 horas

**Total**: 9-14 horas para dashboard 100% funcional com dados reais.

---

**🎯 Objetivo**: Transformar 34 KPIs "fake" em KPIs reais baseados em 23.684 operações da base de produção.**