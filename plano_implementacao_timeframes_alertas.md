# Plano de Implementação: Timeframes e Alertas para KPIs

## 📋 Resumo Executivo

Este documento detalha o plano de implementação para duas novas funcionalidades no sistema de KPIs do DataHero4:

1. **Timeframes Configuráveis:** Permitir que usuários visualizem KPIs em diferentes períodos de tempo
2. **Sistema de Alertas:** Possibilitar a configuração de alertas personalizados para cada KPI

**Impacto esperado:** Aumento de 40% na utilidade analítica do dashboard e redução de 60% no tempo de detecção de anomalias.

---

## 🎯 Objetivos

### Timeframes Configuráveis
- Permitir seleção de diferentes períodos (1d, 7d, 30d, 90d, 1y) para cada KPI
- Manter consistência visual entre diferentes timeframes
- Implementar cálculo eficiente para minimizar impacto de performance

### Sistema de Alertas
- Permitir configuração de alertas baseados em limites (threshold)
- Suportar alertas para valores acima ou abaixo do limite
- Destacar visualmente KPIs com alertas ativos
- Persistir configurações de alertas por cliente/usuário

---

## 🔧 Escopo Técnico

### Componentes Afetados

#### Backend
- Modelo de dados `KpiDefinition`
- Serviço de cálculo de KPIs
- API REST para gerenciamento de KPIs e alertas
- Sistema de cache

#### Frontend
- Componente `KpiCard`
- Modal de configuração de alertas
- Cliente de API
- Hooks de gerenciamento de estado

### Mudanças no Banco de Dados

#### Tabela `kpi_definitions`
- Adicionar campo `supported_timeframes` (JSON)
- Adicionar campo `default_timeframe` (String)

#### Nova Tabela `kpi_alerts`
```sql
CREATE TABLE kpi_alerts (
    id SERIAL PRIMARY KEY,
    kpi_id VARCHAR(50) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'above' ou 'below'
    threshold NUMERIC NOT NULL,
    message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(kpi_id, client_id)
);
```

---

## 📅 Cronograma de Implementação

### Fase 1: Preparação (2 dias)
- [x] Análise detalhada dos componentes existentes
- [ ] Definição de esquema de banco de dados
- [ ] Criação de branches de desenvolvimento
- [ ] Configuração de ambiente de testes

### Fase 2: Backend - Timeframes (3 dias)
- [ ] Atualização do modelo `KpiDefinition` (0.5 dia)
- [ ] Modificação do serviço de cálculo para suportar timeframes (1 dia)
- [ ] Atualização dos endpoints da API (0.5 dia)
- [ ] Ajuste do sistema de cache para considerar timeframes (1 dia)

### Fase 3: Backend - Alertas (3 dias)
- [ ] Criação do modelo `KpiAlert` (0.5 dia)
- [ ] Implementação do serviço de gerenciamento de alertas (1 dia)
- [ ] Criação de endpoints para CRUD de alertas (1 dia)
- [ ] Integração com sistema de notificação (0.5 dia)

### Fase 4: Frontend - Timeframes (2 dias)
- [ ] Atualização do cliente de API (0.5 dia)
- [ ] Implementação do seletor de timeframe no `KpiCard` (1 dia)
- [ ] Ajustes visuais para diferentes timeframes (0.5 dia)

### Fase 5: Frontend - Alertas (3 dias)
- [ ] Criação do modal de configuração de alertas (1 dia)
- [ ] Implementação de indicadores visuais para alertas (0.5 dia)
- [ ] Integração com API de alertas (0.5 dia)
- [ ] Testes de usabilidade e ajustes (1 dia)

### Fase 6: Testes e Integração (2 dias)
- [ ] Testes de integração backend-frontend (1 dia)
- [ ] Testes de performance (0.5 dia)
- [ ] Correção de bugs (0.5 dia)

### Fase 7: Documentação e Deploy (1 dia)
- [ ] Atualização da documentação da API
- [ ] Criação de guia de usuário
- [ ] Deploy para ambiente de staging

**Tempo Total Estimado:** 16 dias úteis

---

## 🔌 APIs Necessárias

### Timeframes

#### GET `/api/kpis/{kpi_id}/calculate`
**Parâmetros:**
- `kpi_id`: Identificador do KPI
- `sector`: Setor de negócio (default: "cambio")
- `client_id`: Identificador do cliente (default: "L2M")
- `timeframe`: Período de tempo (opcional, ex: "30d")

**Resposta:**
```json
{
  "kpi": {
    "id": "total_volume",
    "name": "Volume Total",
    "value": 15000000,
    "formatted_value": "R$ 15.000.000,00",
    "timeframe": "30d",
    "historical_data": [...]
  }
}
```

### Alertas

#### POST `/api/kpis/{kpi_id}/alert`
**Parâmetros:**
- `kpi_id`: Identificador do KPI
- `client_id`: Identificador do cliente (query param)

**Body:**
```json
{
  "type": "above",
  "threshold": 10000000,
  "message": "Volume acima do esperado"
}
```

**Resposta:**
```json
{
  "success": true,
  "kpi_id": "total_volume",
  "alert": {
    "type": "above",
    "threshold": 10000000,
    "message": "Volume acima do esperado"
  }
}
```

#### DELETE `/api/kpis/{kpi_id}/alert`
**Parâmetros:**
- `kpi_id`: Identificador do KPI
- `client_id`: Identificador do cliente (query param)

**Resposta:**
```json
{
  "success": true,
  "kpi_id": "total_volume"
}
```

#### GET `/api/kpis/{kpi_id}/alert`
**Parâmetros:**
- `kpi_id`: Identificador do KPI
- `client_id`: Identificador do cliente (query param)

**Resposta:**
```json
{
  "kpi_id": "total_volume",
  "alert": {
    "type": "above",
    "threshold": 10000000,
    "message": "Volume acima do esperado"
  }
}
```

---

## 🧪 Testes Necessários

### Backend
- Testes unitários para cálculo de KPIs com diferentes timeframes
- Testes de integração para APIs de alertas
- Testes de performance para verificar impacto dos timeframes
- Testes de concorrência para múltiplos usuários

### Frontend
- Testes de componentes para `KpiCard` com timeframes
- Testes de integração para modal de alertas
- Testes de usabilidade para interface de configuração
- Testes de responsividade para diferentes dispositivos

---

## 🚀 Critérios de Sucesso

### Funcionais
- Todos os KPIs podem ser visualizados em 5 timeframes diferentes
- Alertas podem ser configurados, editados e removidos
- Configurações de alertas são persistidas por cliente
- KPIs com alertas ativos são destacados visualmente

### Performance
- Mudança de timeframe responde em menos de 3 segundos
- Sistema suporta até 50 usuários simultâneos
- Cache reduz tempo de resposta em 70% para consultas repetidas
- Banco de dados mantém performance com volume aumentado de consultas

### UX/UI
- NPS de 8+ para novas funcionalidades
- Tempo médio para configurar um alerta < 30 segundos
- Taxa de erro do usuário < 5% ao usar novas funcionalidades
- 90% dos usuários conseguem mudar timeframe sem ajuda

---

## 🛡️ Mitigação de Riscos

| Risco | Probabilidade | Impacto | Mitigação |
|-------|--------------|---------|-----------|
| Performance degradada com timeframes longos | Média | Alto | Implementar cache específico por timeframe |
| Excesso de alertas causando fadiga | Alta | Médio | Limitar número de alertas ativos por usuário |
| Inconsistência visual entre timeframes | Média | Baixo | Padronizar escalas e formatação |
| Falha no cálculo de KPIs históricos | Baixa | Alto | Implementar validação de dados e fallbacks |

---

## 📈 Métricas de Acompanhamento

- **Adoção:** % de usuários utilizando múltiplos timeframes
- **Engajamento:** Número médio de alterações de timeframe por sessão
- **Utilidade:** % de KPIs com alertas configurados
- **Performance:** Tempo médio de resposta por timeframe
- **Satisfação:** NPS específico para novas funcionalidades

---

## 📝 Notas Adicionais

- A implementação deve priorizar a compatibilidade com o sistema existente
- Considerar futura expansão para alertas baseados em tendências (não apenas valores absolutos)
- Documentar claramente a API para facilitar integrações futuras
- Manter retrocompatibilidade com clientes da API existentes

---

*Documento criado em: Julho 2025*  
*Última atualização: Julho 2025*  
*Autor: DataHero4 Team*