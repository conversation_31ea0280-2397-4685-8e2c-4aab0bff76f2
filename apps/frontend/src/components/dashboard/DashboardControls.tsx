import React from 'react';
import { Filter, RefreshCw, Download, Settings, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DashboardControlsProps {
  onRefresh?: () => void;
  onExport?: () => void;
  onAddKpi?: () => void;
  isRefreshing?: boolean;
}

const DashboardControls: React.FC<DashboardControlsProps> = ({
  onRefresh,
  onExport,
  onAddKpi,
  isRefreshing = false
}) => {
  return (
    <div className="flex items-center justify-between mb-6 bg-gray-50 rounded-lg p-3 border border-gray-200">
      {/* Filtros compactos */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Filter className="w-4 h-4" />
          <span>Filtros:</span>
        </div>
        
        <Select defaultValue="today">
          <SelectTrigger className="w-24 h-8 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Hoje</SelectItem>
            <SelectItem value="week">7 dias</SelectItem>
            <SelectItem value="month">30 dias</SelectItem>
            <SelectItem value="quarter">3 meses</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="all">
          <SelectTrigger className="w-28 h-8 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todas</SelectItem>
            <SelectItem value="usd">USD/BRL</SelectItem>
            <SelectItem value="eur">EUR/BRL</SelectItem>
            <SelectItem value="gbp">GBP/BRL</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Ações compactas */}
      <div className="flex items-center gap-2">
        <Button
          variant="default"
          size="sm"
          onClick={onAddKpi}
          className="h-8 px-3 text-xs bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Plus className="w-3 h-3 mr-1" />
          Adicionar KPI
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          disabled={isRefreshing}
          className="h-8 px-3 text-xs"
        >
          <RefreshCw className={`w-3 h-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onExport}
          className="h-8 px-3 text-xs"
        >
          <Download className="w-3 h-3 mr-1" />
          Exportar
        </Button>
      </div>
    </div>
  );
};

export default DashboardControls; 