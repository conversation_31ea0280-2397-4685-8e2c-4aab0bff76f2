import React, { useState } from 'react';
import { Edit3, AlertTriangle, TrendingUp, TrendingDown, Star, GripVertical, Trash2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, ResponsiveContainer, XAxis, YAxis, Tooltip } from 'recharts';
import EditKpiModal from './EditKpiModal';
import AlertModal from './AlertModal';
import { KpiData } from '@/hooks/useKpiData';

interface KpiCardProps {
  kpi: KpiData;
  onTogglePriority: (kpiId: string) => void;
  onRemoveKpi?: (kpiId: string) => void;
  isCompact?: boolean;
}

const KpiCard = ({ kpi, onTogglePriority, onRemoveKpi, isCompact = false }: KpiCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  
  // Usar o valor diretamente sem animação de contador por enquanto
  const animatedValue = kpi.currentValue || 0;

  const formatTooltipValue = (value: number) => {
    return value.toLocaleString('pt-BR', { 
      style: kpi.format === 'currency' ? 'currency' : 'decimal',
      currency: kpi.format === 'currency' ? 'USD' : undefined,
      minimumFractionDigits: kpi.format === 'percentage' ? 2 : 0,
      maximumFractionDigits: kpi.format === 'percentage' ? 2 : 2
    }) + (kpi.format === 'percentage' ? '%' : '');
  };

  interface TooltipPayload {
    value: number;
  }

  interface CustomTooltipProps {
    active?: boolean;
    payload?: TooltipPayload[];
    label?: string;
  }

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">{label}</p>
          <p className="text-sm text-gray-600">
            <span className="font-semibold text-gray-900">
              {formatTooltipValue(payload[0].value)}
            </span>
          </p>
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    const chartProps = {
      data: kpi.chartData,
      margin: { top: 5, right: 5, left: 5, bottom: 5 }
    };

    // Usar sempre tons de cinza para os gráficos
    const strokeColor = '#374151'; // gray-700

    switch (kpi.chartType) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart {...chartProps}>
              <XAxis dataKey="name" hide />
              <YAxis hide />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke={strokeColor}
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 4, stroke: strokeColor, strokeWidth: 2, fill: 'white' }}
              />
            </LineChart>
          </ResponsiveContainer>
        );
      case 'area':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart {...chartProps}>
              <defs>
                <linearGradient id={`gradient-${kpi.id}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor={strokeColor} stopOpacity={0.3}/>
                  <stop offset="100%" stopColor={strokeColor} stopOpacity={0}/>
                </linearGradient>
              </defs>
              <XAxis dataKey="name" hide />
              <YAxis hide />
              <Tooltip content={<CustomTooltip />} />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke={strokeColor}
                strokeWidth={3}
                fill={`url(#gradient-${kpi.id})`}
              />
            </AreaChart>
          </ResponsiveContainer>
        );
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart {...chartProps}>
              <XAxis dataKey="name" hide />
              <YAxis hide />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill={strokeColor} />
            </BarChart>
          </ResponsiveContainer>
        );
      default:
        return null;
    }
  };

  const isAlertTriggered = kpi.alert && kpi.currentValue !== undefined && (
    (kpi.alert.type === 'above' && kpi.currentValue > kpi.alert.threshold) ||
    (kpi.alert.type === 'below' && kpi.currentValue < kpi.alert.threshold)
  );

  const getTrendIcon = () => {
    if (kpi.trend === 'up') return <TrendingUp className="w-4 h-4 text-green-600" />;
    if (kpi.trend === 'down') return <TrendingDown className="w-4 h-4 text-red-600" />;
    return null;
  };

  const handleRemoveConfirm = () => {
    if (onRemoveKpi) {
      onRemoveKpi(kpi.id);
    }
    setShowRemoveConfirm(false);
  };

  return (
    <>
      <div className="animate-fade-in">
        <Card 
          className={`relative overflow-hidden transition-all duration-300 hover:shadow-xl cursor-move bg-white rounded-xl ${
            isAlertTriggered ? 'border-red-500 shadow-red-100' : 'border-gray-200 hover:border-gray-300'
          } ${kpi.isPriority ? 'ring-2 ring-blue-400 shadow-blue-100' : ''}`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
        <CardContent className={isCompact ? "p-4 h-full flex flex-col justify-between" : "p-6"}>
          {/* Indicador de arrastar */}
          {isHovered && !isCompact && (
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
              <GripVertical className="w-4 h-4 text-gray-400" />
            </div>
          )}

          {/* Badge de Prioridade */}
          {kpi.isPriority && !isCompact && (
            <div className="absolute top-4 right-4 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1">
              <Star className="w-3 h-3 fill-blue-600" />
              Prioritário
            </div>
          )}

          {/* Alertas apenas para cards não compactos */}
          {!isCompact && isAlertTriggered && (
            <div className="mb-4 animate-fade-in">
              <Alert className="border-red-500 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {kpi.alert?.message || `Valor ${kpi.alert?.type === 'above' ? 'acima' : 'abaixo'} do limite definido`}
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Layout compacto vs normal */}
          {isCompact ? (
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-semibold text-gray-900 truncate">{kpi.title}</h3>
                {getTrendIcon()}
              </div>
              
              <div className="flex-1 flex flex-col justify-center">
                <div 
                  className="text-xl font-bold text-gray-900 mb-1"
                  key={kpi.currentValue}
                >
                  {animatedValue.toLocaleString('pt-BR', { 
                    style: kpi.format === 'currency' ? 'currency' : 'decimal',
                    currency: kpi.format === 'currency' ? 'USD' : undefined,
                    minimumFractionDigits: kpi.format === 'percentage' ? 2 : 0,
                    maximumFractionDigits: kpi.format === 'percentage' ? 2 : 2
                  })}
                  {kpi.format === 'percentage' && '%'}
                </div>
                
                {kpi.changePercent && (
                  <div className="text-xs text-gray-500">
                    <span className={kpi.changePercent > 0 ? 'text-green-600' : 'text-red-600'}>
                      {kpi.changePercent > 0 ? '+' : ''}{kpi.changePercent.toFixed(1)}%
                    </span>
                  </div>
                )}
              </div>

              <div className="h-12 mt-2">
                {renderChart()}
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{kpi.title}</h3>
                  <p className="text-sm text-gray-500">{kpi.description}</p>
                </div>
                {getTrendIcon()}
              </div>

              <div className="mb-4">
                <div 
                  className="text-3xl font-bold text-gray-900 mb-1"
                  key={kpi.currentValue}
                >
                  {animatedValue.toLocaleString('pt-BR', { 
                    style: kpi.format === 'currency' ? 'currency' : 'decimal',
                    currency: kpi.format === 'currency' ? 'USD' : undefined,
                    minimumFractionDigits: kpi.format === 'percentage' ? 2 : 0,
                    maximumFractionDigits: kpi.format === 'percentage' ? 2 : 2
                  })}
                  {kpi.format === 'percentage' && '%'}
                </div>
                <div className="text-sm text-gray-500">
                  {kpi.changePercent && (
                    <span className={kpi.changePercent > 0 ? 'text-green-600' : 'text-red-600'}>
                      {kpi.changePercent > 0 ? '+' : ''}{kpi.changePercent.toFixed(2)}% vs período anterior
                    </span>
                  )}
                </div>
              </div>

              <div className="h-24 mb-4">
                {renderChart()}
              </div>
            </>
          )}

          {/* Hover Actions - Mais Sutis */}
          {isHovered && (
            <>
              {/* Botões nas bordas superiores */}
              <div className="absolute top-2 left-2 animate-fade-in">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => onTogglePriority(kpi.id)}
                  className="bg-white/90 hover:bg-white shadow-sm border-0 h-8 px-2"
                >
                  <Star className={`w-3 h-3 ${kpi.isPriority ? 'fill-amber-500 text-amber-500' : 'text-gray-400'}`} />
                </Button>
              </div>

              {/* Botões na borda inferior direita */}
              <div className="absolute bottom-2 right-2 flex gap-2 animate-fade-in">
                {onRemoveKpi && (
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => setShowRemoveConfirm(true)}
                    className="bg-white/90 hover:bg-red-50 shadow-sm border-0 h-8 px-2 group"
                  >
                    <Trash2 className="w-3 h-3 text-gray-600 group-hover:text-red-600" />
                  </Button>
                )}
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setShowEditModal(true)}
                  className="bg-white/90 hover:bg-white shadow-sm border-0 h-8 px-2"
                >
                  <Edit3 className="w-3 h-3 text-gray-600" />
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setShowAlertModal(true)}
                  className="bg-white/90 hover:bg-white shadow-sm border-0 h-8 px-2"
                >
                  <AlertTriangle className="w-3 h-3 text-gray-600" />
                </Button>
              </div>
            </>
          )}
        </CardContent>
        </Card>
      </div>

      <EditKpiModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        kpi={kpi}
      />

      <AlertModal
        isOpen={showAlertModal}
        onClose={() => setShowAlertModal(false)}
        kpi={kpi}
      />

      <AlertDialog open={showRemoveConfirm} onOpenChange={setShowRemoveConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remover KPI do Dashboard</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja remover <strong>"{kpi.title}"</strong> do dashboard? 
              Esta ação não pode ser desfeita, mas você pode adicionar o KPI novamente depois.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemoveConfirm}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Remover
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default KpiCard;
