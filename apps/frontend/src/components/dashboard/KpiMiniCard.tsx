import React from 'react';
import { Check, Plus } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { type AvailableKpi } from '@/lib/api';

interface KpiMiniCardProps {
  kpi: AvailableKpi;
  isSelected: boolean;
  isExisting: boolean;
  onClick: (kpiId: string) => void;
}

// Mapeamento de categorias para cores e ícones
const KPI_CATEGORIES = {
  'Operacionais': { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: '⚙️' },
  'Performance': { color: 'bg-green-100 text-green-800 border-green-200', icon: '📈' },
  'Volume': { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: '📊' },
  'Risco': { color: 'bg-red-100 text-red-800 border-red-200', icon: '🛡️' },
  'Eficiência': { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: '⚡' },
  'Qualidade': { color: 'bg-indigo-100 text-indigo-800 border-indigo-200', icon: '✅' },
  'Crescimento': { color: 'bg-pink-100 text-pink-800 border-pink-200', icon: '🚀' }
} as const;

// Mapeamento de KPIs específicos para ícones
const KPI_ICONS = {
  'total_volume': '📊',
  'volume_by_currency': '💱',
  'average_ticket': '🎫',
  'growth_percentage': '📈',
  'average_spread': '💰',
  'gross_margin': '💵',
  'net_margin': '🏦',
  'operations_roi': '📊',
  'cost_per_operation': '💳',
  'cost_to_income_ratio': '⚖️',
  'transaction_count': '🔢',
  'processing_time': '⏱️',
  'throughput': '⚡',
  'approval_rate': '✅',
  'rejection_rate': '❌',
  'operational_efficiency': '⚙️',
  'capacity_utilization': '📊',
  'var': '🛡️',
  'exposure_concentration': '🎯',
  'currency_risk': '💱',
  'counterparty_risk': '🤝',
  'compliance_score': '📋'
} as const;

const KpiMiniCard: React.FC<KpiMiniCardProps> = ({
  kpi,
  isSelected,
  isExisting,
  onClick
}) => {
  const categoryInfo = KPI_CATEGORIES[kpi.category as keyof typeof KPI_CATEGORIES] || KPI_CATEGORIES['Operacionais'];
  const kpiIcon = KPI_ICONS[kpi.id as keyof typeof KPI_ICONS] || categoryInfo.icon;

  const handleClick = () => {
    if (!isExisting) {
      onClick(kpi.id);
    }
  };

  const getCardStyles = () => {
    if (isExisting) {
      return 'border-green-300 bg-green-50 cursor-not-allowed opacity-75';
    }
    if (isSelected) {
      return 'border-blue-500 bg-blue-50 shadow-md cursor-pointer transform scale-[1.02]';
    }
    return 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm cursor-pointer transition-all duration-200';
  };

  const getIconStyles = () => {
    if (isExisting) return 'text-green-600';
    if (isSelected) return 'text-blue-600';
    return 'text-gray-600';
  };

  return (
    <Card 
      className={`relative h-40 ${getCardStyles()}`}
      onClick={handleClick}
    >
      <CardContent className="p-4 h-full flex flex-col justify-between">
        {/* Header com ícone e status */}
        <div className="flex items-start justify-between mb-2">
          <div className="text-2xl">{kpiIcon}</div>
          <div className="flex items-center">
            {isExisting && <Check className="w-4 h-4 text-green-600" />}
            {isSelected && !isExisting && <Check className="w-4 h-4 text-blue-600" />}
            {!isSelected && !isExisting && <Plus className="w-4 h-4 text-gray-400" />}
          </div>
        </div>

        {/* Título do KPI */}
        <div className="flex-1">
          <h3 className={`font-semibold text-sm leading-tight mb-2 ${getIconStyles()}`}>
            {kpi.name}
          </h3>
          
          {/* Descrição */}
          <p className="text-xs text-gray-600 leading-relaxed line-clamp-3">
            {kpi.description}
          </p>
        </div>

        {/* Footer com categoria */}
        <div className="mt-3">
          <Badge 
            variant="secondary" 
            className={`text-xs px-2 py-1 ${categoryInfo.color}`}
          >
            {categoryInfo.icon} {kpi.category}
          </Badge>
        </div>

        {/* Overlay para status existente */}
        {isExisting && (
          <div className="absolute inset-0 bg-green-100 bg-opacity-50 rounded-lg flex items-center justify-center">
            <div className="bg-green-600 text-white px-3 py-1 rounded-full text-xs font-medium">
              Já adicionado
            </div>
          </div>
        )}

        {/* Borda de seleção */}
        {isSelected && !isExisting && (
          <div className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none" />
        )}
      </CardContent>
    </Card>
  );
};

export default KpiMiniCard;