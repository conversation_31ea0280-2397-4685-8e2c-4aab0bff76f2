import React from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface KpiSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  resultCount: number;
  totalCount: number;
}

const KpiSearchInput: React.FC<KpiSearchInputProps> = ({
  value,
  onChange,
  resultCount,
  totalCount
}) => {
  const handleClear = () => {
    onChange('');
  };

  return (
    <div className="space-y-3">
      {/* Campo de busca */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        
        <Input
          type="text"
          placeholder="Buscar KPIs... (ex: volume, spread, margem)"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="pl-10 pr-10 h-12 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500"
        />
        
        {value && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="h-6 w-6 p-0 hover:bg-gray-100"
            >
              <X className="h-4 w-4 text-gray-400" />
            </Button>
          </div>
        )}
      </div>

      {/* Contador de resultados */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <div className="flex items-center space-x-2">
          <span className="font-medium">
            {value ? (
              <>
                {resultCount} de {totalCount} KPIs encontrados
              </>
            ) : (
              <>
                {totalCount} KPIs disponíveis
              </>
            )}
          </span>
          
          {value && resultCount === 0 && (
            <span className="text-amber-600">
              • Nenhum resultado encontrado
            </span>
          )}
        </div>

        {/* Dicas de busca */}
        {!value && (
          <div className="text-xs text-gray-400">
            Dica: tente "volume", "margem", "risco"
          </div>
        )}
      </div>

      {/* Separador visual */}
      <div className="border-t border-gray-200" />
    </div>
  );
};

export default KpiSearchInput;