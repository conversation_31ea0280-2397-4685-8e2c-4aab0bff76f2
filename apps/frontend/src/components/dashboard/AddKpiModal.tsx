import React from 'react';
import { Plus, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import KpiMiniCard from './KpiMiniCard';
import { type AvailableKpi } from '@/lib/api';
import KpiSearchInput from './KpiSearchInput';
import { useKpiSelection } from '@/hooks/useKpiSelection';
import { useAvailableKpis } from '@/hooks/useAvailableKpis';

interface AddKpiModalProps {
  isOpen: boolean;
  onClose: () => void;
  onKpisSelected: (kpiIds: string[]) => void;
  existingKpiIds: string[];
}

const AddKpiModal: React.FC<AddKpiModalProps> = ({
  isOpen,
  onClose,
  onKpisSelected,
  existingKpiIds
}) => {
  // Buscar KPIs disponíveis
  const { availableKpis, isLoading: isLoadingKpis, error } = useAvailableKpis();
  
  // Gerenciar seleção e busca
  const {
    searchTerm,
    selectedKpiIds,
    filteredKpis,
    selectedKpis,
    stats,
    setSearchTerm,
    toggleKpiSelection,
    clearSelections,
    reset,
    isKpiSelected,
    isKpiExisting
  } = useKpiSelection({ availableKpis, existingKpiIds });

  // Handlers
  const handleClose = () => {
    reset();
    onClose();
  };

  const handleAddSelected = () => {
    if (selectedKpiIds.length > 0) {
      onKpisSelected(selectedKpiIds);
      reset();
      onClose();
    }
  };

  const handleKpiClick = (kpiId: string) => {
    if (!isKpiExisting(kpiId)) {
      toggleKpiSelection(kpiId);
    }
  };

  // Loading state
  if (isLoadingKpis) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-6xl max-h-[80vh]">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-gray-600">Carregando KPIs disponíveis...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Error state
  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-6xl max-h-[80vh]">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-red-600 mb-4">Erro ao carregar KPIs: {error}</p>
              <Button onClick={() => window.location.reload()}>
                Tentar novamente
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[80vh] flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Plus className="w-5 h-5 text-blue-600" />
            Adicionar KPIs ao Dashboard
          </DialogTitle>
          <DialogDescription>
            Selecione os KPIs que deseja adicionar ao seu dashboard. Você pode buscar por nome, descrição ou categoria.
          </DialogDescription>
        </DialogHeader>

        {/* Campo de busca */}
        <div className="pb-4">
          <KpiSearchInput
            value={searchTerm}
            onChange={setSearchTerm}
            resultCount={stats.totalFiltered}
            totalCount={stats.totalAvailable}
          />
        </div>

        {/* Grid de KPIs */}
        <ScrollArea className="flex-1 pr-4">
          {stats.hasResults ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pb-4">
              {filteredKpis.map((kpi) => (
                <KpiMiniCard
                  key={kpi.id}
                  kpi={kpi}
                  isSelected={isKpiSelected(kpi.id)}
                  isExisting={isKpiExisting(kpi.id)}
                  onClick={handleKpiClick}
                />
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum KPI encontrado
                </h3>
                <p className="text-gray-500 max-w-sm">
                  {stats.hasSearch 
                    ? `Nenhum KPI encontrado para "${searchTerm}". Tente termos como "volume", "margem" ou "risco".`
                    : 'Não há KPIs disponíveis no momento.'
                  }
                </p>
                {stats.hasSearch && (
                  <Button
                    variant="outline"
                    onClick={() => setSearchTerm('')}
                    className="mt-4"
                  >
                    Limpar busca
                  </Button>
                )}
              </div>
            </div>
          )}
        </ScrollArea>

        {/* Footer com ações */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            {stats.hasSelections && (
              <span>
                {stats.totalSelected} KPI{stats.totalSelected !== 1 ? 's' : ''} selecionado{stats.totalSelected !== 1 ? 's' : ''}
              </span>
            )}
            {stats.totalExisting > 0 && (
              <span className="ml-2 text-green-600">
                • {stats.totalExisting} já no dashboard
              </span>
            )}
          </div>

          <div className="flex items-center gap-2">
            {stats.hasSelections && (
              <Button
                variant="outline"
                onClick={clearSelections}
                size="sm"
              >
                Limpar seleção
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={handleClose}
              size="sm"
            >
              Cancelar
            </Button>
            
            <Button
              onClick={handleAddSelected}
              disabled={!stats.hasSelections}
              size="sm"
              className="min-w-[120px]"
            >
              <Plus className="w-4 h-4 mr-2" />
              Adicionar {stats.hasSelections ? stats.totalSelected : ''}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddKpiModal;