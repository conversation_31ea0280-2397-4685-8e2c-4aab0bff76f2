import React from 'react';
import Kpi<PERSON>ard from './KpiCard';
import { useKpiData } from '@/hooks/useKpiData';

interface KpiGridProps {
  onRemoveKpi?: (kpiId: string) => void;
}

const KpiGrid = ({ onRemoveKpi }: KpiGridProps) => {
  console.log('KpiGrid component rendering');

  const { kpis, isLoading, error, togglePriority, refreshKpis } = useKpiData();

  console.log('KpiGrid - isLoading:', isLoading, 'error:', error, 'kpis:', kpis);

  if (isLoading) {
    console.log('KpiGrid - showing loading state');
    return (
      <div className="space-y-8">
        {/* Loading skeleton com melhor layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="h-48 bg-gray-200 rounded-xl animate-pulse" />
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 rounded-xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    console.log('KpiGrid - showing error state:', error);
    return (
      <div className="space-y-8">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Erro ao carregar KPIs
          </h3>
          <p className="text-red-600 mb-4">
            {error}
          </p>
          <button
            onClick={refreshKpis}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  console.log('KpiGrid - rendering KPIs, count:', kpis.length);

  const handleDragStart = (e: React.DragEvent, kpiId: string) => {
    e.dataTransfer.setData('text/plain', kpiId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetKpiId: string) => {
    e.preventDefault();
    const draggedKpiId = e.dataTransfer.getData('text/plain');
    if (draggedKpiId !== targetKpiId) {
      reorderKpis(draggedKpiId, targetKpiId);
    }
  };

  console.log('KpiGrid - showing KPI cards, count:', kpis.length);

  // Separar KPIs por prioridade para melhor hierarquia visual
  const priorityKpis = kpis.filter(kpi => kpi.isPriority);
  const regularKpis = kpis.filter(kpi => !kpi.isPriority);

  return (
    <div className="space-y-8">
      {/* KPIs Prioritários - Layout destacado */}
      {priorityKpis.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
            <h3 className="text-lg font-semibold text-gray-900">KPIs Prioritários</h3>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {priorityKpis.map((kpi) => (
              <div
                key={kpi.id}
                className="transform transition-all duration-300 hover:scale-[1.02]"
                draggable
                onDragStart={(e) => handleDragStart(e, kpi.id)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, kpi.id)}
              >
                <KpiCard kpi={kpi} onTogglePriority={togglePriority} onRemoveKpi={onRemoveKpi} />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* KPIs Regulares - Grid quadrado responsivo */}
      {regularKpis.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="w-1 h-6 bg-gray-300 rounded-full"></div>
            <h3 className="text-lg font-semibold text-gray-900">Métricas Gerais</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {regularKpis.map((kpi) => (
              <div
                key={kpi.id}
                className="aspect-square transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                draggable
                onDragStart={(e) => handleDragStart(e, kpi.id)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, kpi.id)}
              >
                <KpiCard kpi={kpi} onTogglePriority={togglePriority} onRemoveKpi={onRemoveKpi} isCompact={true} />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Estado vazio melhorado */}
      {kpis.length === 0 && (
        <div className="text-center py-16">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum KPI disponível</h3>
          <p className="text-gray-500">Os dados estão sendo carregados ou não há métricas configuradas.</p>
        </div>
      )}
    </div>
  );
};

export default KpiGrid;
