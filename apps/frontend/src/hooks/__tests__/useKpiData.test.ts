/**
 * Unit tests for useKpiData hook
 * Tests the real API integration and data handling
 */

import { renderHook, waitFor } from '@testing-library/react';
import { useKpiData } from '../useKpiData';

// Mock the API module
jest.mock('@/lib/api', () => ({
  getDashboardKpis: jest.fn(),
}));

import { getDashboardKpis } from '@/lib/api';

const mockGetDashboardKpis = getDashboardKpis as jest.MockedFunction<typeof getDashboardKpis>;

describe('useKpiData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should load KPIs successfully', async () => {
    // Mock successful API response
    const mockKpis = [
      {
        id: 'total_volume',
        title: 'Volume Total Negociado',
        description: 'Mede o tamanho da operação',
        currentValue: 2850000,
        format: 'currency' as const,
        changePercent: 9.6,
        trend: 'up' as const,
        chartType: 'area' as const,
        chartData: [
          { name: 'Jan', value: 2200000 },
          { name: 'Fev', value: 2350000 },
        ],
        isPriority: true,
        order: 0,
        category: 'operational',
        unit: 'Valor monetário (R$, US$, etc.)',
        frequency: 'Diária/Semanal/Mensal'
      }
    ];

    mockGetDashboardKpis.mockResolvedValue({
      kpis: mockKpis,
      total_count: 1,
      sector: 'cambio',
      client_id: 'L2M',
      timeframe: '1d',
      generated_at: new Date().toISOString()
    });

    const { result } = renderHook(() => useKpiData());

    // Initially loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.kpis).toEqual([]);
    expect(result.current.error).toBe(null);

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check loaded data
    expect(result.current.kpis).toEqual(mockKpis);
    expect(result.current.error).toBe(null);
    expect(mockGetDashboardKpis).toHaveBeenCalledTimes(1);
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    const errorMessage = 'Failed to load KPIs';
    mockGetDashboardKpis.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useKpiData());

    // Wait for error state
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check error state
    expect(result.current.kpis).toEqual([]);
    expect(result.current.error).toBe(errorMessage);
  });

  it('should sort KPIs by priority and order', async () => {
    // Mock KPIs with different priorities and orders
    const mockKpis = [
      {
        id: 'kpi3',
        title: 'KPI 3',
        description: 'Description 3',
        currentValue: 100,
        format: 'number' as const,
        trend: 'stable' as const,
        chartType: 'line' as const,
        chartData: [],
        isPriority: false,
        order: 1,
        category: 'operational',
      },
      {
        id: 'kpi1',
        title: 'KPI 1',
        description: 'Description 1',
        currentValue: 100,
        format: 'number' as const,
        trend: 'stable' as const,
        chartType: 'line' as const,
        chartData: [],
        isPriority: true,
        order: 0,
        category: 'operational',
      },
      {
        id: 'kpi2',
        title: 'KPI 2',
        description: 'Description 2',
        currentValue: 100,
        format: 'number' as const,
        trend: 'stable' as const,
        chartType: 'line' as const,
        chartData: [],
        isPriority: true,
        order: 1,
        category: 'operational',
      }
    ];

    mockGetDashboardKpis.mockResolvedValue({
      kpis: mockKpis,
      total_count: 3,
      sector: 'cambio',
      client_id: 'L2M',
      timeframe: '1d',
      generated_at: new Date().toISOString()
    });

    const { result } = renderHook(() => useKpiData());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check sorting: priority KPIs first, then by order
    const sortedKpis = result.current.kpis;
    expect(sortedKpis[0].id).toBe('kpi1'); // Priority, order 0
    expect(sortedKpis[1].id).toBe('kpi2'); // Priority, order 1
    expect(sortedKpis[2].id).toBe('kpi3'); // Not priority, order 1
  });

  it('should provide refresh functionality', async () => {
    const mockKpis = [
      {
        id: 'test_kpi',
        title: 'Test KPI',
        description: 'Test Description',
        currentValue: 100,
        format: 'number' as const,
        trend: 'stable' as const,
        chartType: 'line' as const,
        chartData: [],
        isPriority: false,
        order: 0,
        category: 'operational',
      }
    ];

    mockGetDashboardKpis.mockResolvedValue({
      kpis: mockKpis,
      total_count: 1,
      sector: 'cambio',
      client_id: 'L2M',
      timeframe: '1d',
      generated_at: new Date().toISOString()
    });

    const { result } = renderHook(() => useKpiData());

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Call refresh
    result.current.refreshKpis();

    // Should be loading again
    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should have called API twice (initial + refresh)
    expect(mockGetDashboardKpis).toHaveBeenCalledTimes(2);
  });

  it('should provide toggle priority functionality', async () => {
    const mockKpis = [
      {
        id: 'test_kpi',
        title: 'Test KPI',
        description: 'Test Description',
        currentValue: 100,
        format: 'number' as const,
        trend: 'stable' as const,
        chartType: 'line' as const,
        chartData: [],
        isPriority: false,
        order: 0,
        category: 'operational',
      }
    ];

    mockGetDashboardKpis.mockResolvedValue({
      kpis: mockKpis,
      total_count: 1,
      sector: 'cambio',
      client_id: 'L2M',
      timeframe: '1d',
      generated_at: new Date().toISOString()
    });

    const { result } = renderHook(() => useKpiData());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Initially not priority
    expect(result.current.kpis[0].isPriority).toBe(false);

    // Toggle priority
    result.current.togglePriority('test_kpi');

    // Should now be priority
    expect(result.current.kpis[0].isPriority).toBe(true);

    // Toggle again
    result.current.togglePriority('test_kpi');

    // Should be back to not priority
    expect(result.current.kpis[0].isPriority).toBe(false);
  });
});
