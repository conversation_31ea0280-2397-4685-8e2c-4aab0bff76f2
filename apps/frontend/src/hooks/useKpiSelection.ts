import { useState, useMemo, useCallback } from 'react';
import { type AvailableKpi } from '@/lib/api';

interface UseKpiSelectionProps {
  availableKpis: AvailableKpi[];
  existingKpiIds: string[];
}

export const useKpiSelection = ({ availableKpis, existingKpiIds }: UseKpiSelectionProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedKpiIds, setSelectedKpiIds] = useState<string[]>([]);

  // Filtro dos KPIs baseado no termo de busca
  const filteredKpis = useMemo(() => {
    if (!searchTerm.trim()) {
      return availableKpis;
    }

    const term = searchTerm.toLowerCase().trim();
    
    return availableKpis.filter(kpi => 
      kpi.name.toLowerCase().includes(term) ||
      kpi.description.toLowerCase().includes(term) ||
      kpi.category.toLowerCase().includes(term) ||
      kpi.id.toLowerCase().includes(term)
    );
  }, [availableKpis, searchTerm]);

  // Toggle seleção de um KPI
  const toggleKpiSelection = useCallback((kpiId: string) => {
    setSelectedKpiIds(prev => {
      if (prev.includes(kpiId)) {
        return prev.filter(id => id !== kpiId);
      } else {
        return [...prev, kpiId];
      }
    });
  }, []);

  // Limpar seleções
  const clearSelections = useCallback(() => {
    setSelectedKpiIds([]);
  }, []);

  // Limpar busca
  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  // Reset completo
  const reset = useCallback(() => {
    setSearchTerm('');
    setSelectedKpiIds([]);
  }, []);

  // Verificar se um KPI está selecionado
  const isKpiSelected = useCallback((kpiId: string) => {
    return selectedKpiIds.includes(kpiId);
  }, [selectedKpiIds]);

  // Verificar se um KPI já existe no dashboard
  const isKpiExisting = useCallback((kpiId: string) => {
    return existingKpiIds.includes(kpiId);
  }, [existingKpiIds]);

  // Obter KPIs selecionados (objetos completos)
  const selectedKpis = useMemo(() => {
    return availableKpis.filter(kpi => selectedKpiIds.includes(kpi.id));
  }, [availableKpis, selectedKpiIds]);

  // Estatísticas
  const stats = useMemo(() => ({
    totalAvailable: availableKpis.length,
    totalFiltered: filteredKpis.length,
    totalSelected: selectedKpiIds.length,
    totalExisting: existingKpiIds.length,
    hasSearch: searchTerm.trim().length > 0,
    hasSelections: selectedKpiIds.length > 0,
    hasResults: filteredKpis.length > 0
  }), [availableKpis.length, filteredKpis.length, selectedKpiIds.length, existingKpiIds.length, searchTerm]);

  return {
    // Estado
    searchTerm,
    selectedKpiIds,
    filteredKpis,
    selectedKpis,
    stats,
    
    // Ações
    setSearchTerm,
    toggleKpiSelection,
    clearSelections,
    clearSearch,
    reset,
    
    // Verificações
    isKpiSelected,
    isKpiExisting
  };
};