import { useState, useEffect } from 'react';
import { getAvailableKpis, type AvailableKpi } from '@/lib/api';

export const useAvailableKpis = (client_id: string = 'L2M', sector: string = 'cambio') => {
  const [availableKpis, setAvailableKpis] = useState<AvailableKpi[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAvailableKpis = async () => {
      console.log('🔄 [useAvailableKpis] Loading available KPIs...');
      setIsLoading(true);
      setError(null);

      try {
        const kpis = await getAvailableKpis(client_id, sector);
        console.log('✅ [useAvailableKpis] Loaded KPIs:', kpis.length);
        setAvailableKpis(kpis);
      } catch (err) {
        console.error('❌ [useAvailableKpis] Error loading KPIs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load available KPIs');
        
        // Fallback para dados mock se a API falhar
        setAvailableKpis(getMockKpis());
      } finally {
        setIsLoading(false);
      }
    };

    loadAvailableKpis();
  }, [client_id, sector]);

  const refresh = async () => {
    await loadAvailableKpis();
  };

  return {
    availableKpis,
    isLoading,
    error,
    refresh
  };
};

// Dados mock para fallback baseados na configuração real dos 34 KPIs
const getMockKpis = (): AvailableKpi[] => [
  // Volume Metrics
  {
    id: 'total_volume',
    name: 'Volume Total Negociado',
    description: 'Mede o tamanho da operação e é o indicador primário de crescimento',
    category: 'Volume',
    unit: 'Valor monetário (USD)',
    icon: '📊'
  },
  {
    id: 'volume_by_currency',
    name: 'Volume por Moeda',
    description: 'Análise de concentração e exposição por moeda',
    category: 'Volume',
    unit: 'Valor monetário (USD)',
    icon: '💱'
  },
  {
    id: 'average_ticket',
    name: 'Ticket Médio',
    description: 'Indica perfil de clientes e operações',
    category: 'Volume',
    unit: 'Valor monetário (USD)',
    icon: '🎫'
  },
  {
    id: 'growth_percentage',
    name: 'Percentual de Crescimento',
    description: 'Compara desempenho entre períodos',
    category: 'Crescimento',
    unit: 'Percentual (%)',
    icon: '📈'
  },

  // Performance Metrics
  {
    id: 'average_spread',
    name: 'Spread Médio',
    description: 'Principal indicador de rentabilidade',
    category: 'Performance',
    unit: 'Percentual (%)',
    icon: '💰'
  },
  {
    id: 'gross_margin',
    name: 'Margem Bruta',
    description: 'Mede o ganho total em spreads',
    category: 'Performance',
    unit: 'Valor monetário (BRL)',
    icon: '💵'
  },
  {
    id: 'net_margin',
    name: 'Margem Líquida',
    description: 'Rentabilidade real das operações',
    category: 'Performance',
    unit: 'Valor monetário (BRL)',
    icon: '🏦'
  },
  {
    id: 'operations_roi',
    name: 'ROI das Operações',
    description: 'Retorno sobre investimento das operações',
    category: 'Performance',
    unit: 'Percentual (%)',
    icon: '📊'
  },
  {
    id: 'cost_per_operation',
    name: 'Custo por Operação',
    description: 'Custo médio para processar cada operação',
    category: 'Eficiência',
    unit: 'Valor monetário (BRL)',
    icon: '💳'
  },
  {
    id: 'cost_to_income_ratio',
    name: 'Relação Custo/Receita',
    description: 'Eficiência operacional geral',
    category: 'Eficiência',
    unit: 'Percentual (%)',
    icon: '⚖️'
  },

  // Operational Metrics
  {
    id: 'transaction_count',
    name: 'Número de Transações',
    description: 'Volume total de operações processadas',
    category: 'Operacionais',
    unit: 'Número',
    icon: '🔢'
  },
  {
    id: 'processing_time',
    name: 'Tempo de Processamento',
    description: 'Tempo médio para completar uma operação',
    category: 'Operacionais',
    unit: 'Dias',
    icon: '⏱️'
  },
  {
    id: 'throughput',
    name: 'Throughput',
    description: 'Número de operações processadas por período',
    category: 'Operacionais',
    unit: 'Operações/dia',
    icon: '⚡'
  },
  {
    id: 'approval_rate',
    name: 'Taxa de Aprovação',
    description: 'Percentual de operações aprovadas',
    category: 'Qualidade',
    unit: 'Percentual (%)',
    icon: '✅'
  },
  {
    id: 'rejection_rate',
    name: 'Taxa de Rejeição',
    description: 'Percentual de operações rejeitadas',
    category: 'Qualidade',
    unit: 'Percentual (%)',
    icon: '❌'
  },
  {
    id: 'operational_efficiency',
    name: 'Eficiência Operacional',
    description: 'Medida geral de eficiência do processo',
    category: 'Eficiência',
    unit: 'Percentual (%)',
    icon: '⚙️'
  },
  {
    id: 'capacity_utilization',
    name: 'Utilização de Capacidade',
    description: 'Percentual da capacidade operacional utilizada',
    category: 'Eficiência',
    unit: 'Percentual (%)',
    icon: '📊'
  },

  // Risk Metrics
  {
    id: 'var',
    name: 'Value at Risk (VaR)',
    description: 'Medida de risco de perda potencial',
    category: 'Risco',
    unit: 'Valor monetário (USD)',
    icon: '🛡️'
  },
  {
    id: 'exposure_concentration',
    name: 'Concentração de Exposição',
    description: 'Grau de concentração em moedas específicas',
    category: 'Risco',
    unit: 'Índice (0-1)',
    icon: '🎯'
  },
  {
    id: 'currency_risk',
    name: 'Risco Cambial',
    description: 'Volatilidade e risco associado às moedas',
    category: 'Risco',
    unit: 'Percentual (%)',
    icon: '💱'
  },
  {
    id: 'counterparty_risk',
    name: 'Risco de Contraparte',
    description: 'Avaliação de risco dos clientes',
    category: 'Risco',
    unit: 'Score (0-100)',
    icon: '🤝'
  },
  {
    id: 'compliance_score',
    name: 'Score de Compliance',
    description: 'Pontuação de conformidade regulatória',
    category: 'Risco',
    unit: 'Score (0-100)',
    icon: '📋'
  }
];

const loadAvailableKpis = async () => {
  // Esta função seria movida para dentro do useEffect se precisar ser reutilizada
};