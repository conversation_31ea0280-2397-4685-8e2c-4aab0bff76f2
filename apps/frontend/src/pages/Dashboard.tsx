import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import KpiGrid from '@/components/dashboard/KpiGrid';
import DashboardControls from '@/components/dashboard/DashboardControls';
import AddKpiModal from '@/components/dashboard/AddKpiModal';
import { useKpiData } from '@/hooks/useKpiData';

const Dashboard = () => {
  const navigate = useNavigate();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showAddKpiModal, setShowAddKpiModal] = useState(false);
  
  // Acessar funções do hook de KPIs
  const { kpis, addKpis, removeKpi, refresh } = useKpiData();
  
  console.log('Dashboard component rendering');

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refresh();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExport = () => {
    // Implementar lógica de exportação
    console.log('Exportando dados...');
  };

  const handleAddKpi = () => {
    setShowAddKpiModal(true);
  };

  const handleKpisSelected = async (kpiIds: string[]) => {
    try {
      await addKpis(kpiIds);
      console.log(`✅ ${kpiIds.length} KPIs adicionados com sucesso`);
    } catch (error) {
      console.error('❌ Erro ao adicionar KPIs:', error);
    }
  };

  const handleRemoveKpi = (kpiId: string) => {
    removeKpi(kpiId);
    console.log(`🗑️ KPI ${kpiId} removido`);
  };

  // Lista dos IDs dos KPIs existentes para o modal
  const existingKpiIds = kpis.map(kpi => kpi.id);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header simples sem linha separadora */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Ícone do DataHero */}
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            
            {/* Indicadores de status */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Sistema Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Atualizando</span>
              </div>
            </div>
          </div>
          
          {/* Botão para ir às perguntas */}
          <Button 
            variant="outline" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="w-4 h-4" />
            Fazer Perguntas
          </Button>
        </div>
      </div>

      {/* Conteúdo principal com melhor espaçamento */}
      <div className="pb-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">


          {/* Controles do Dashboard */}
          <DashboardControls 
            onRefresh={handleRefresh}
            onExport={handleExport}
            onAddKpi={handleAddKpi}
            isRefreshing={isRefreshing}
          />

          {/* Grid de KPIs */}
          <KpiGrid onRemoveKpi={handleRemoveKpi} />
        </div>
      </div>

      {/* Modal para adicionar KPIs */}
      <AddKpiModal
        isOpen={showAddKpiModal}
        onClose={() => setShowAddKpiModal(false)}
        onKpisSelected={handleKpisSelected}
        existingKpiIds={existingKpiIds}
      />
    </div>
  );
};

export default Dashboard;
