#!/usr/bin/env node
/**
 * Test script to verify frontend-backend API connectivity
 * Tests the dashboard KPI endpoint from the frontend perspective
 */

const API_BASE_URL = 'http://localhost:8000';

async function testApiConnection() {
  console.log('🧪 Testing DataHero4 Frontend-Backend API Connection');
  console.log('=' .repeat(60));
  
  try {
    // Test 1: Basic connectivity
    console.log('📡 Test 1: Basic Backend Connectivity');
    const pingResponse = await fetch(`${API_BASE_URL}/ping`);
    
    if (!pingResponse.ok) {
      throw new Error(`Backend not responding: ${pingResponse.status}`);
    }
    
    const pingData = await pingResponse.json();
    console.log('✅ Backend is responding:', pingData.message);
    
    // Test 2: Dashboard KPIs endpoint
    console.log('\n📊 Test 2: Dashboard KPIs Endpoint');
    const kpiResponse = await fetch(`${API_BASE_URL}/api/dashboard/kpis?sector=cambio&client_id=L2M&timeframe=1d`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      }
    });
    
    if (!kpiResponse.ok) {
      throw new Error(`KPI endpoint failed: ${kpiResponse.status}`);
    }
    
    const kpiData = await kpiResponse.json();
    console.log(`✅ KPI endpoint working: ${kpiData.total_count} KPIs received`);
    
    // Test 3: Validate KPI data structure
    console.log('\n🔍 Test 3: KPI Data Structure Validation');
    
    if (!kpiData.kpis || !Array.isArray(kpiData.kpis)) {
      throw new Error('Invalid KPI data structure');
    }
    
    const firstKpi = kpiData.kpis[0];
    const requiredFields = ['id', 'title', 'currentValue', 'format', 'trend', 'chartData'];
    
    for (const field of requiredFields) {
      if (!(field in firstKpi)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    console.log('✅ KPI data structure is valid');
    
    // Test 4: Priority KPIs
    console.log('\n⭐ Test 4: Priority KPIs');
    const priorityKpis = kpiData.kpis.filter(kpi => kpi.isPriority);
    console.log(`✅ Found ${priorityKpis.length} priority KPIs:`);
    
    priorityKpis.forEach(kpi => {
      console.log(`   - ${kpi.title}: ${formatValue(kpi.currentValue, kpi.format)}`);
    });
    
    // Test 5: Chart data validation
    console.log('\n📈 Test 5: Chart Data Validation');
    const kpiWithChart = kpiData.kpis.find(kpi => kpi.chartData && kpi.chartData.length > 0);
    
    if (!kpiWithChart) {
      throw new Error('No KPI found with chart data');
    }
    
    console.log(`✅ Chart data found for "${kpiWithChart.title}": ${kpiWithChart.chartData.length} data points`);
    
    // Test 6: CORS headers
    console.log('\n🌐 Test 6: CORS Configuration');
    const corsHeaders = kpiResponse.headers.get('access-control-allow-origin');
    
    if (corsHeaders !== 'http://localhost:3000') {
      console.log('⚠️  CORS might be misconfigured:', corsHeaders);
    } else {
      console.log('✅ CORS properly configured for frontend');
    }
    
    // Summary
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 ALL TESTS PASSED!');
    console.log(`📊 Total KPIs: ${kpiData.total_count}`);
    console.log(`⭐ Priority KPIs: ${priorityKpis.length}`);
    console.log(`🏢 Sector: ${kpiData.sector}`);
    console.log(`👤 Client: ${kpiData.client_id}`);
    console.log(`⏰ Generated: ${new Date(kpiData.generated_at).toLocaleString()}`);
    console.log('\n✅ Frontend should be able to load dashboard data successfully!');
    
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Check if backend is running on port 8000');
    console.error('   2. Check if frontend is running on port 3000');
    console.error('   3. Verify environment variables in frontend/.env');
    console.error('   4. Check browser console for CORS errors');
    process.exit(1);
  }
}

function formatValue(value, format) {
  switch (format) {
    case 'currency':
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    case 'percentage':
      return `${value.toFixed(1)}%`;
    default:
      return value.toLocaleString();
  }
}

// Run the test
testApiConnection();
