"""
Hybrid FastAPI webapp for debugging - Tests if full imports work.
"""
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import os
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create hybrid FastAPI app
app = FastAPI(
    title="DataHero4 Hybrid Debug",
    description="Hybrid version for debugging full imports",
    version="2.0.0-hybrid"
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "DataHero4 Hybrid Debug - Testing Imports", "status": "healthy"}

@app.get("/health")
async def health():
    """Health check with import test."""
    import_status = "unknown"
    error_msg = None
    
    try:
        # Test if we can import the main API
        from src.interfaces.api import app as main_app
        import_status = "success"
        logger.info("✅ Successfully imported main API")
    except Exception as e:
        import_status = "failed"
        error_msg = str(e)
        logger.error(f"❌ Failed to import main API: {e}")
    
    return {
        "status": "healthy",
        "version": "2.0.0-hybrid",
        "environment": os.getenv("ENVIRONMENT", "unknown"),
        "port": os.getenv("PORT", "8000"),
        "import_test": {
            "status": import_status,
            "error": error_msg
        }
    }

@app.get("/test-full-app")
async def test_full_app():
    """Test if we can load the full application."""
    try:
        from src.interfaces.api import app as main_app
        return {"status": "success", "message": "Full app import successful"}
    except Exception as e:
        return {"status": "error", "message": f"Import failed: {str(e)}"}

# Minimal startup event
@app.on_event("startup")
async def startup():
    """Hybrid startup - test imports."""
    logger.info("🚀 Hybrid DataHero4 starting up...")
    logger.info(f"Environment: {os.getenv('ENVIRONMENT', 'unknown')}")
    logger.info(f"Port: {os.getenv('PORT', '8000')}")
    
    # Test import without failing startup
    try:
        from src.interfaces.api import app as main_app
        logger.info("✅ Main app import successful during startup")
    except Exception as e:
        logger.error(f"❌ Main app import failed during startup: {e}")
    
    logger.info("✅ Hybrid startup completed")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("PORT", 8000)))