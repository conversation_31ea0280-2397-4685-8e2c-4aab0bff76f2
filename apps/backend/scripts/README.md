# Scripts DataHero4

Este diretório contém scripts utilitários para desenvolvimento, manutenção e operação do sistema DataHero4.

## ⚡ **NOVO: Scripts do Sistema de Snapshot**

🎯 **Automação Completa**: Scripts para geração, validação e monitoramento de snapshots

### Scripts de Snapshot
- ✅ **generate_daily_snapshot.py**: Geração manual e automática de snapshots
- ✅ **validate_kpi_calculations.py**: Validação de cálculos de KPIs com dados reais
- ✅ **../cron/generate_snapshot.sh**: Script para cron job (automação diária)

### Uso dos Scripts de Snapshot

#### Geração de Snapshot
```bash
# Geração básica
poetry run python scripts/generate_daily_snapshot.py

# Com cliente específico
poetry run python scripts/generate_daily_snapshot.py --client L2M

# Modo verboso (recomendado)
poetry run python scripts/generate_daily_snapshot.py --verbose
```

#### Validação de KPIs
```bash
# Validar cálculos comparando snapshot vs banco
poetry run python scripts/validate_kpi_calculations.py
```

#### Automação (Cron Job)
```bash
# Executar script de cron manualmente
bash cron/generate_snapshot.sh

# Configurar cron job (produção)
# 0 3 * * * /app/apps/backend/cron/generate_snapshot.sh
```

## 📊 Scripts de Análise e Debug

### Performance e Monitoramento
- `profile_pipeline_performance.py` - Análise de performance do pipeline
- `monitor_nivel2.py` - Monitoramento do sistema Nivel2
- `simple_profiling_test.py` - Testes simples de profiling

### Debug e Investigação
- `debug_api_feedback.py` - Debug do sistema de feedback da API
- `debug_sql_generation.py` - Debug da geração de SQL
- `debug_workflow_state_tracking.py` - Debug do tracking de estado
- `investigate_cache_contamination.py` - Investigação de contaminação de cache
- `investigate_join_problem.py` - Investigação de problemas de JOIN

### Testes Específicos
- `test_business_analyst_eur_usd.py` - Teste específico EUR/USD
- `test_complex_question.py` - Teste de perguntas complexas
- `test_context_preservation_manual.py` - Teste manual de preservação de contexto
- `test_real_euro_dollar_question.py` - Teste real de pergunta EUR/USD

## 🔧 Scripts de Configuração e Setup

### Configuração de Sistema
- `setup_nivel2.py` - Setup do sistema Nivel2
- `validate_nivel2.py` - Validação do sistema Nivel2
- `diagnose_database_config.py` - Diagnóstico de configuração do banco

### Migração e Cache
- `migrate_feedback_cache.py` - Migração do cache de feedback
- `cache_warming_script.py` - Aquecimento do cache
- `clean_learning_tables.py` - Limpeza das tabelas de learning

## 📈 Scripts de Análise de Dados

### Feedback e Learning
- `analyze_negative_feedback.py` - Análise de feedback negativo
- `check_feedback_data.py` - Verificação de dados de feedback
- `deep_feedback_investigation.py` - Investigação profunda de feedback

### Schema e Estrutura
- `check_database_schema.py` - Verificação do schema do banco
- `check_client_database_schema.py` - Verificação do schema do cliente

## 🧪 Scripts de Teste e Validação

### Testes End-to-End
- `test_pipeline_end_to_end.py` - Teste completo do pipeline
- `test_template_optimization_e2e.py` - Teste E2E de otimização de templates
- `test_integrated_validation.py` - Teste de validação integrada

### Testes de Componentes
- `test_sql_executor.py` - Teste do executor SQL
- `test_sql_validation.py` - Teste de validação SQL
- `test_chart_visualization.py` - Teste de visualização de gráficos

## 🎯 Scripts de Demonstração

### Demos e Exemplos
- `demo_elegant_solution.py` - Demonstração de solução elegante
- `generate_golden_set.py` - Geração de conjunto dourado
- `run_datahero_direct.py` - Execução direta do DataHero

## 📋 Como Usar

### Pré-requisitos
```bash
# Instalar dependências
poetry install

# Configurar variáveis de ambiente
cp .env.example .env
# Editar .env com suas configurações
```

### Execução Geral
```bash
# Do diretório apps/backend
cd apps/backend

# Executar qualquer script
poetry run python scripts/nome_do_script.py

# Com argumentos
poetry run python scripts/nome_do_script.py --arg1 valor1 --arg2 valor2
```

### Logs e Output
- Logs são salvos em `logs/` quando aplicável
- Outputs de snapshot são salvos em `data/snapshots/`
- Resultados de validação são exibidos no console

## 🚨 Scripts Críticos (Produção)

### Automação Diária
1. **generate_daily_snapshot.py** - Execução diária às 3h via cron
2. **validate_kpi_calculations.py** - Validação semanal recomendada

### Monitoramento
1. **monitor_nivel2.py** - Monitoramento contínuo
2. **profile_pipeline_performance.py** - Análise de performance

### Manutenção
1. **clean_learning_tables.py** - Limpeza mensal
2. **cache_warming_script.py** - Aquecimento após deploy

## 📞 Suporte

Para problemas com scripts:
1. Verificar logs em `logs/`
2. Executar com `--verbose` quando disponível
3. Verificar configurações em `.env`
4. Consultar documentação específica em `docs/`
