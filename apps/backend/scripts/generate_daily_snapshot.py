#!/usr/bin/env python3
"""
Script para gerar snapshot diário dos KPIs críticos.

Uso:
    python scripts/generate_daily_snapshot.py [--client CLIENT_ID]
"""

import sys
import argparse
import logging
from pathlib import Path

# Adicionar diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.services.snapshot_service import SnapshotService

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(description='Gera snapshot diário de KPIs')
    parser.add_argument(
        '--client',
        default='L2M',
        help='ID do cliente (default: L2M)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Modo verboso'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        logger.info("🚀 Iniciando geração de snapshot diário")
        logger.info(f"📊 Cliente: {args.client}")
        
        service = SnapshotService()
        snapshot = service.generate_daily_snapshot(args.client)
        
        if snapshot['metadata'].get('error'):
            logger.error(f"❌ Erro: {snapshot['metadata']['error_message']}")
            return 1
        
        logger.info(f"✅ Snapshot gerado com sucesso!")
        logger.info(f"📈 KPIs calculados: {snapshot['summary']['total_calculated']}/{snapshot['summary']['total_expected']}")
        logger.info(f"📊 Taxa de sucesso: {snapshot['summary']['success_rate']:.1f}%")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Erro fatal: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
