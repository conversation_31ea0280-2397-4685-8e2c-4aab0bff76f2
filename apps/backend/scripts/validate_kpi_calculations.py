#!/usr/bin/env python3
"""
Script para validar os cálculos dos KPIs críticos usando conexão direta ao PostgreSQL.

Verifica se os valores calculados pelo sistema de snapshot estão corretos
comparando com queries SQL diretas no banco de dados.
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime, timedelta
import psycopg2
from psycopg2.extras import RealDictCursor

# Adicionar diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.config.critical_kpis import CriticalKpisConfig

# Configuração do banco L2M
DB_CONFIG = {
    'host': 'l2m-homol.c50woq6q6gnz.us-east-1.rds.amazonaws.com',
    'port': 5432,
    'user': 'postgres',
    'password': os.getenv('L2M_DB_PASSWORD', 'DaB9cWeNfcNdxoTubNV96qeQ0sNGnhAZ4IwehAdb5ofEoCfMYM'),
    'database': 'l2m_prod'
}

def get_db_connection():
    """Conecta ao banco PostgreSQL L2M."""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return None

def validate_total_volume():
    """Valida o cálculo do Volume Total Negociado."""
    print("🔍 Validando Volume Total Negociado...")
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Query para volume total - últimos 30 dias
            query = """
            SELECT 
                SUM(valor_operacao) as total_volume,
                COUNT(*) as total_operacoes,
                AVG(valor_operacao) as ticket_medio
            FROM operacoes 
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'
            AND status = 'concluida'
            """
            
            cur.execute(query)
            result = cur.fetchone()
            
            if result:
                print(f"✅ Volume Total (30 dias): R$ {result['total_volume']:,.2f}")
                print(f"📊 Total de Operações: {result['total_operacoes']:,}")
                print(f"💰 Ticket Médio: R$ {result['ticket_medio']:,.2f}")
                return {
                    'total_volume': float(result['total_volume']) if result['total_volume'] else 0,
                    'total_operacoes': result['total_operacoes'],
                    'ticket_medio': float(result['ticket_medio']) if result['ticket_medio'] else 0
                }
            else:
                print("❌ Nenhum dado encontrado")
                return None
                
    except Exception as e:
        print(f"❌ Erro na query: {e}")
        return None
    finally:
        conn.close()

def validate_average_spread():
    """Valida o cálculo do Spread Médio."""
    print("\n🔍 Validando Spread Médio...")
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Query para spread médio
            query = """
            SELECT 
                AVG(spread_percentual) as spread_medio,
                MIN(spread_percentual) as spread_min,
                MAX(spread_percentual) as spread_max,
                COUNT(*) as total_operacoes_com_spread
            FROM operacoes 
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'
            AND status = 'concluida'
            AND spread_percentual IS NOT NULL
            AND spread_percentual > 0
            """
            
            cur.execute(query)
            result = cur.fetchone()
            
            if result:
                print(f"✅ Spread Médio: {result['spread_medio']:.2f}%")
                print(f"📊 Spread Mín/Máx: {result['spread_min']:.2f}% / {result['spread_max']:.2f}%")
                print(f"🔢 Operações com spread: {result['total_operacoes_com_spread']:,}")
                return {
                    'spread_medio': float(result['spread_medio']) if result['spread_medio'] else 0,
                    'spread_min': float(result['spread_min']) if result['spread_min'] else 0,
                    'spread_max': float(result['spread_max']) if result['spread_max'] else 0
                }
            else:
                print("❌ Nenhum dado de spread encontrado")
                return None
                
    except Exception as e:
        print(f"❌ Erro na query: {e}")
        return None
    finally:
        conn.close()

def validate_conversion_rate():
    """Valida o cálculo da Taxa de Conversão."""
    print("\n🔍 Validando Taxa de Conversão...")
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Query para taxa de conversão (cotações que viraram operações)
            query = """
            SELECT 
                COUNT(DISTINCT c.id) as total_cotacoes,
                COUNT(DISTINCT o.cotacao_id) as cotacoes_convertidas,
                ROUND(
                    (COUNT(DISTINCT o.cotacao_id)::float / COUNT(DISTINCT c.id)::float) * 100, 
                    2
                ) as taxa_conversao
            FROM cotacoes c
            LEFT JOIN operacoes o ON c.id = o.cotacao_id AND o.status = 'concluida'
            WHERE c.data_cotacao >= CURRENT_DATE - INTERVAL '30 days'
            """
            
            cur.execute(query)
            result = cur.fetchone()
            
            if result:
                print(f"✅ Taxa de Conversão: {result['taxa_conversao']}%")
                print(f"📊 Cotações totais: {result['total_cotacoes']:,}")
                print(f"🎯 Cotações convertidas: {result['cotacoes_convertidas']:,}")
                return {
                    'taxa_conversao': float(result['taxa_conversao']) if result['taxa_conversao'] else 0,
                    'total_cotacoes': result['total_cotacoes'],
                    'cotacoes_convertidas': result['cotacoes_convertidas']
                }
            else:
                print("❌ Nenhum dado de conversão encontrado")
                return None
                
    except Exception as e:
        print(f"❌ Erro na query: {e}")
        return None
    finally:
        conn.close()

def validate_retention_rate():
    """Valida o cálculo da Taxa de Retenção."""
    print("\n🔍 Validando Taxa de Retenção...")
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Query para taxa de retenção (clientes que fizeram operações nos últimos 30 e 60 dias)
            query = """
            WITH clientes_60_dias AS (
                SELECT DISTINCT cliente_id
                FROM operacoes 
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '60 days'
                AND data_operacao < CURRENT_DATE - INTERVAL '30 days'
                AND status = 'concluida'
            ),
            clientes_30_dias AS (
                SELECT DISTINCT cliente_id
                FROM operacoes 
                WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'
                AND status = 'concluida'
            )
            SELECT 
                COUNT(DISTINCT c60.cliente_id) as clientes_periodo_anterior,
                COUNT(DISTINCT c30.cliente_id) as clientes_retidos,
                ROUND(
                    (COUNT(DISTINCT c30.cliente_id)::float / COUNT(DISTINCT c60.cliente_id)::float) * 100, 
                    2
                ) as taxa_retencao
            FROM clientes_60_dias c60
            LEFT JOIN clientes_30_dias c30 ON c60.cliente_id = c30.cliente_id
            """
            
            cur.execute(query)
            result = cur.fetchone()
            
            if result:
                print(f"✅ Taxa de Retenção: {result['taxa_retencao']}%")
                print(f"📊 Clientes período anterior: {result['clientes_periodo_anterior']:,}")
                print(f"🔄 Clientes retidos: {result['clientes_retidos']:,}")
                return {
                    'taxa_retencao': float(result['taxa_retencao']) if result['taxa_retencao'] else 0,
                    'clientes_periodo_anterior': result['clientes_periodo_anterior'],
                    'clientes_retidos': result['clientes_retidos']
                }
            else:
                print("❌ Nenhum dado de retenção encontrado")
                return None
                
    except Exception as e:
        print(f"❌ Erro na query: {e}")
        return None
    finally:
        conn.close()

def validate_operations_per_analyst():
    """Valida o cálculo de Operações por Analista."""
    print("\n🔍 Validando Operações por Analista...")
    
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Query para operações por analista
            query = """
            SELECT 
                COUNT(*) as total_operacoes,
                COUNT(DISTINCT analista_id) as total_analistas,
                ROUND(COUNT(*)::float / COUNT(DISTINCT analista_id)::float, 0) as operacoes_por_analista
            FROM operacoes 
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '30 days'
            AND status = 'concluida'
            AND analista_id IS NOT NULL
            """
            
            cur.execute(query)
            result = cur.fetchone()
            
            if result:
                print(f"✅ Operações por Analista: {result['operacoes_por_analista']:,.0f}")
                print(f"📊 Total de Operações: {result['total_operacoes']:,}")
                print(f"👥 Total de Analistas: {result['total_analistas']:,}")
                return {
                    'operacoes_por_analista': float(result['operacoes_por_analista']) if result['operacoes_por_analista'] else 0,
                    'total_operacoes': result['total_operacoes'],
                    'total_analistas': result['total_analistas']
                }
            else:
                print("❌ Nenhum dado de analistas encontrado")
                return None
                
    except Exception as e:
        print(f"❌ Erro na query: {e}")
        return None
    finally:
        conn.close()

def load_current_snapshot():
    """Carrega o snapshot atual para comparação."""
    snapshot_path = Path("data/snapshots/latest.json")
    
    if not snapshot_path.exists():
        print("❌ Snapshot atual não encontrado")
        return None
    
    try:
        with open(snapshot_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar snapshot: {e}")
        return None

def compare_values(snapshot_value, calculated_value, kpi_name, tolerance=0.05):
    """Compara valores do snapshot com valores calculados."""
    if snapshot_value is None or calculated_value is None:
        return False
    
    # Calcular diferença percentual
    if snapshot_value == 0 and calculated_value == 0:
        return True
    
    if snapshot_value == 0:
        diff_percent = float('inf')
    else:
        diff_percent = abs(snapshot_value - calculated_value) / snapshot_value
    
    is_valid = diff_percent <= tolerance
    
    print(f"📊 {kpi_name}:")
    print(f"   Snapshot: {snapshot_value:,.2f}")
    print(f"   Calculado: {calculated_value:,.2f}")
    print(f"   Diferença: {diff_percent*100:.2f}%")
    print(f"   Status: {'✅ OK' if is_valid else '❌ DIVERGÊNCIA'}")
    
    return is_valid

def main():
    """Função principal de validação."""
    print("🔍 VALIDAÇÃO DOS CÁLCULOS DE KPI - DataHero4")
    print("=" * 60)
    
    # Carregar snapshot atual
    snapshot = load_current_snapshot()
    if not snapshot:
        print("❌ Não foi possível carregar o snapshot para comparação")
        return 1
    
    print(f"📸 Snapshot carregado: {snapshot['metadata']['date']} {snapshot['metadata']['time']}")
    print()
    
    # Validar cada KPI
    validation_results = {}
    
    # 1. Volume Total
    volume_data = validate_total_volume()
    if volume_data and 'total_volume' in snapshot['kpis']:
        snapshot_volume = snapshot['kpis']['total_volume']['value']
        validation_results['total_volume'] = compare_values(
            snapshot_volume, volume_data['total_volume'], "Volume Total"
        )
    
    # 2. Ticket Médio (derivado do volume)
    if volume_data and 'average_ticket' in snapshot['kpis']:
        snapshot_ticket = snapshot['kpis']['average_ticket']['value']
        validation_results['average_ticket'] = compare_values(
            snapshot_ticket, volume_data['ticket_medio'], "Ticket Médio"
        )
    
    # 3. Spread Médio
    spread_data = validate_average_spread()
    if spread_data and 'average_spread' in snapshot['kpis']:
        snapshot_spread = snapshot['kpis']['average_spread']['value']
        validation_results['average_spread'] = compare_values(
            snapshot_spread, spread_data['spread_medio'], "Spread Médio"
        )
    
    # 4. Taxa de Conversão
    conversion_data = validate_conversion_rate()
    if conversion_data and 'conversion_rate' in snapshot['kpis']:
        snapshot_conversion = snapshot['kpis']['conversion_rate']['value']
        validation_results['conversion_rate'] = compare_values(
            snapshot_conversion, conversion_data['taxa_conversao'], "Taxa de Conversão"
        )
    
    # 5. Taxa de Retenção
    retention_data = validate_retention_rate()
    if retention_data and 'retention_rate' in snapshot['kpis']:
        snapshot_retention = snapshot['kpis']['retention_rate']['value']
        validation_results['retention_rate'] = compare_values(
            snapshot_retention, retention_data['taxa_retencao'], "Taxa de Retenção"
        )
    
    # 6. Operações por Analista
    analyst_data = validate_operations_per_analyst()
    if analyst_data and 'operations_per_analyst' in snapshot['kpis']:
        snapshot_analyst = snapshot['kpis']['operations_per_analyst']['value']
        validation_results['operations_per_analyst'] = compare_values(
            snapshot_analyst, analyst_data['operacoes_por_analista'], "Operações por Analista"
        )
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DA VALIDAÇÃO")
    print("=" * 60)
    
    total_kpis = len(validation_results)
    valid_kpis = sum(validation_results.values())
    
    print(f"KPIs validados: {valid_kpis}/{total_kpis}")
    print(f"Taxa de precisão: {(valid_kpis/total_kpis)*100:.1f}%")
    
    if valid_kpis == total_kpis:
        print("✅ TODOS OS KPIs ESTÃO CORRETOS!")
        return 0
    else:
        print("❌ ALGUNS KPIs APRESENTAM DIVERGÊNCIAS")
        print("💡 Recomenda-se revisar os cálculos dos KPIs com divergências")
        return 1

if __name__ == "__main__":
    sys.exit(main())
