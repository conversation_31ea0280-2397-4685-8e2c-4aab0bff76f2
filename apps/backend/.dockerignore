# Docker ignore file for backend
# Ignore files not needed in production

# Development files
.env
.env.local
.env.development
*.log
logs/
temp/
cache/
backup/

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# Virtual environments
venv/
env/
ENV/
.venv/

# Testing
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.hypothesis/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Documentation
docs/
*.md
!README.md

# Git
.git/
.gitignore

# Frontend
../frontend/
../../node_modules/ 