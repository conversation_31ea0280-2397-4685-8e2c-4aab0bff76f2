#!/bin/bash
# Script para cron job de geração de snapshot

# Detectar ambiente (produção vs desenvolvimento)
if [ -f "/app/.env" ]; then
    # Ambiente de produção
    export $(cat /app/.env | xargs)
    PROJECT_DIR="/app/apps/backend"
    PYTHON_CMD="python"
else
    # Ambiente de desenvolvimento
    PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    PYTHON_CMD="poetry run python"
fi

# Diretório do projeto
cd "$PROJECT_DIR"

# Criar diretório de logs se não existir
mkdir -p logs

# Executar geração de snapshot
$PYTHON_CMD scripts/generate_daily_snapshot.py --client L2M >> logs/snapshot_generation.log 2>&1

# Verificar sucesso
if [ $? -eq 0 ]; then
    echo "[$(date)] ✅ Snapshot gerado com sucesso" >> logs/snapshot_generation.log
else
    echo "[$(date)] ❌ Erro na geração do snapshot" >> logs/snapshot_generation.log
    # Opcional: enviar notificação de erro
fi
