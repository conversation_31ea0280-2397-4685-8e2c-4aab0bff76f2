"""
Minimal FastAPI webapp for Railway debugging - No mounting, minimal startup.
"""
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import os
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create minimal FastAPI app
app = FastAPI(
    title="DataHero4 Minimal Debug",
    description="Minimal version for Railway 502 debugging",
    version="1.0.0-debug"
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "DataHero4 Minimal Debug - OK", "status": "healthy"}

@app.get("/health")
async def health():
    """Minimal health check."""
    return {
        "status": "healthy",
        "version": "1.0.0-debug",
        "environment": os.getenv("ENVIRONMENT", "unknown"),
        "port": os.getenv("PORT", "8000")
    }

@app.get("/docs-redirect")
async def docs_redirect():
    """Simple endpoint to test API responsiveness."""
    return {"message": "API is responding", "docs": "/docs"}

# Minimal startup event
@app.on_event("startup")
async def startup():
    """Minimal startup - just logging."""
    logger.info("🚀 Minimal DataHero4 starting up...")
    logger.info(f"Environment: {os.getenv('ENVIRONMENT', 'unknown')}")
    logger.info(f"Port: {os.getenv('PORT', '8000')}")
    logger.info("✅ Minimal startup completed")

@app.on_event("shutdown")
async def shutdown():
    """Minimal shutdown."""
    logger.info("🔄 Minimal DataHero4 shutting down...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("PORT", 8000)))