"""
FastAPI webapp for LangGraph deployment following best practices.
"""
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from src.interfaces.api import app as main_app

# Define lifespan for resource management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    # Initialize resources on startup
    print("🚀 DataHero4 starting up...")
    yield
    # Clean up resources on shutdown
    print("🔄 DataHero4 shutting down...")

# Create FastAPI app instance with lifespan
app = FastAPI(
    title="DataHero4 LangGraph API",
    description="DataHero4 LangGraph deployment with optimized workflow",
    version="4.0.0",
    lifespan=lifespan
)

# Mount the main application
app.mount("/", main_app)

if __name__ == "__main__":
    # Import uvicorn dynamically to avoid linter error
    try:
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8000)
    except ImportError:
        print("❌ uvicorn not found. Install with: poetry add uvicorn") 