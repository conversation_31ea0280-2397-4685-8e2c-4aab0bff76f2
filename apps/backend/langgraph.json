{"dependencies": ["."], "graphs": {"datahero": "./src/graphs/optimized_workflow.py:create_optimized_workflow"}, "env": ".env", "http": {"app": "./src/interfaces/api.py:app", "disable_assistants": false, "disable_threads": false, "disable_runs": false, "disable_store": false, "disable_meta": false, "cors": {"allow_origins": ["*"], "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allow_headers": ["*"]}}, "image_distro": "wolfi"}