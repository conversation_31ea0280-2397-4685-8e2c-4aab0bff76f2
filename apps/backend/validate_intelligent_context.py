#!/usr/bin/env python3
"""
Validação rápida do IntelligentContextDetector
Verifica syntax e estrutura básica
"""

import sys
import os

def validate_syntax():
    """Valida syntax do arquivo principal."""
    print("🔍 Validating syntax...")
    
    try:
        # Test basic import
        sys.path.append('src')
        
        # Check if file compiles
        with open('src/services/intelligent_context_detector.py', 'r') as f:
            source = f.read()
        
        # Compile to check syntax
        compile(source, 'src/services/intelligent_context_detector.py', 'exec')
        print("✅ Syntax validation passed!")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def validate_context_preservation_integration():
    """Valida integração com context preservation engine."""
    print("\n🔍 Validating context preservation integration...")
    
    try:
        with open('src/services/context_preservation_engine.py', 'r') as f:
            source = f.read()
        
        # Check key integrations
        checks = [
            'from .intelligent_context_detector import IntelligentContextDetector',
            'self.intelligent_detector = IntelligentContextDetector()',
            'self.enable_intelligent_detection = True',
            'intelligent_result = self.intelligent_detector.detect_context',
            '_filter_temporal_entities(entities, temporal_intent, intelligent_result)'
        ]
        
        for check in checks:
            if check not in source:
                print(f"❌ Missing integration: {check}")
                return False
        
        print("✅ Context preservation integration validated!")
        return True
        
    except Exception as e:
        print(f"❌ Integration validation error: {e}")
        return False

def validate_structure():
    """Valida estrutura dos arquivos."""
    print("\n🔍 Validating file structure...")
    
    required_files = [
        'src/services/intelligent_context_detector.py',
        'src/services/context_preservation_engine.py'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ Missing file: {file_path}")
            return False
    
    print("✅ File structure validated!")
    return True

def check_railway_compatibility():
    """Verifica padrões de compatibilidade Railway."""
    print("\n🔍 Checking Railway compatibility patterns...")
    
    try:
        with open('src/services/intelligent_context_detector.py', 'r') as f:
            source = f.read()
        
        # Check for problematic async patterns
        problematic_patterns = [
            'asyncio.run(',
            'async def',
            'await ',
            'AsyncOpenAI',
            'ainvoke'
        ]
        
        issues = []
        for pattern in problematic_patterns:
            if pattern in source:
                issues.append(pattern)
        
        if issues:
            print(f"⚠️ Found potential async patterns: {issues}")
            print("Note: These should be handled carefully for Railway compatibility")
        else:
            print("✅ No problematic async patterns found!")
        
        # Check for sync patterns
        sync_patterns = [
            'ThreadPoolExecutor',
            'invoke(',
            'def detect_context(',
            'def _llm_'
        ]
        
        sync_found = sum(1 for pattern in sync_patterns if pattern in source)
        print(f"✅ Found {sync_found} sync-compatible patterns")
        
        return True
        
    except Exception as e:
        print(f"❌ Railway compatibility check error: {e}")
        return False

def main():
    """Executa todas as validações."""
    print("🚀 Starting IntelligentContextDetector validation...\n")
    
    all_passed = True
    
    # Basic validations
    if not validate_structure():
        all_passed = False
    
    if not validate_syntax():
        all_passed = False
    
    if not validate_context_preservation_integration():
        all_passed = False
        
    if not check_railway_compatibility():
        all_passed = False
    
    # Summary
    print("\n" + "="*50)
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("\nIntelligent Context Detection system is ready:")
        print("✅ Syntax correct")
        print("✅ Integration complete") 
        print("✅ Railway compatible")
        print("✅ Sync-only design")
        
        print("\nNext steps:")
        print("1. Test with actual LLM calls")
        print("2. Deploy to Railway")
        print("3. Monitor performance")
        
    else:
        print("❌ SOME VALIDATIONS FAILED!")
        print("Please fix the issues above before deployment.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)