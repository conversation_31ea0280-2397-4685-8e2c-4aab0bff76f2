# 📸 Sistema de Snapshot DataHero4

## 🎯 Visão Geral

O Sistema de Snapshot do DataHero4 é uma solução de alta performance que pré-calcula os 6 KPIs críticos do negócio de câmbio, oferecendo respostas instantâneas (< 20ms) em vez dos 5-60 segundos originais.

## 🚀 Benefícios

- **Performance**: Redução de 5-60s para < 20ms (99.9% de melhoria)
- **Confiabilidade**: Dados reais do cliente L2M
- **Escalabilidade**: Sistema preparado para múltiplos clientes
- **Manutenibilidade**: Código limpo e bem testado

## 📊 KPIs Críticos

O sistema calcula 6 KPIs essenciais para operadoras de câmbio:

1. **Volume Total Negociado** - Indicador primário de crescimento
2. **Spread Médio** - Principal indicador de rentabilidade  
3. **Taxa de Conversão** - Eficácia comercial
4. **Ticket Médio** - Perfil de clientes e operações
5. **Taxa de Retenção** - Fidelização de clientes
6. **Operações por Analista** - Produtividade da equipe

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cron Job      │───▶│  Snapshot        │───▶│   JSON Files    │
│   (03:00 BRT)   │    │  Service         │    │   + Latest      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Database       │
                       │   (PostgreSQL)   │
                       └──────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │───▶│   API Endpoint   │───▶│   Snapshot      │
│   Dashboard     │    │   /snapshot      │    │   Files         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📁 Estrutura de Arquivos

```
apps/backend/
├── src/
│   ├── config/
│   │   ├── critical_kpis.py      # Configuração dos KPIs críticos
│   │   └── feature_flags.py      # Feature flags do sistema
│   ├── services/
│   │   └── snapshot_service.py   # Serviço principal de snapshots
│   ├── api/
│   │   └── dashboard_snapshot.py # Endpoints da API
│   └── tests/
│       └── test_snapshot_service.py # Testes unitários
├── scripts/
│   └── generate_daily_snapshot.py   # Script de geração
├── cron/
│   └── generate_snapshot.sh        # Script do cron job
├── data/
│   └── snapshots/                  # Arquivos de snapshot
│       ├── latest.json            # Snapshot mais recente
│       └── snapshot_YYYYMMDD.json # Snapshots datados
└── logs/
    └── snapshot_generation.log    # Logs de geração
```

## 🔧 Configuração

### 1. Variáveis de Ambiente

```bash
# Feature flags
DASHBOARD_SNAPSHOT=true
DEV_MODE=true

# Database credentials
DB_LEARNING_HOST=your-host
DB_LEARNING_PORT=5432
DB_LEARNING_USER=postgres
DB_LEARNING_PASSWORD=your-password
DB_LEARNING_NAME=your-database
```

### 2. Cron Job

Adicionar ao crontab do servidor:

```cron
# Gerar snapshot todos os dias às 3h da manhã (horário de Brasília)
0 3 * * * /app/apps/backend/cron/generate_snapshot.sh
```

## 🚀 Uso

### Geração Manual

```bash
# Gerar snapshot manualmente
cd apps/backend
poetry run python scripts/generate_daily_snapshot.py

# Com cliente específico
poetry run python scripts/generate_daily_snapshot.py --client L2M

# Modo verboso
poetry run python scripts/generate_daily_snapshot.py --verbose
```

### API Endpoints

#### GET /api/dashboard/snapshot
Retorna o snapshot mais recente dos KPIs críticos.

**Parâmetros:**
- `client_id` (opcional): ID do cliente (default: "L2M")
- `regenerate` (opcional): Força regeneração (apenas dev mode)

**Resposta:**
```json
{
  "success": true,
  "data": {
    "total_volume": {
      "value": 26757442623.04,
      "formatted": "R$ 26.8B",
      "title": "Volume Total Negociado",
      "icon": "TrendingUp",
      "format": "currency"
    }
  },
  "metadata": {
    "generated_at": "2025-07-06 15:45:34",
    "kpi_count": 6,
    "cache_ttl": 3600
  }
}
```

#### GET /api/dashboard/snapshot/info
Retorna informações sobre o snapshot atual.

#### GET /api/dashboard/snapshot/health
Retorna status de saúde do sistema de snapshots.

## 🧪 Testes

```bash
# Executar testes unitários
poetry run python -m pytest src/tests/test_snapshot_service.py -v

# Testar performance da API
time curl "http://localhost:8000/api/dashboard/snapshot"

# Verificar saúde do sistema
curl "http://localhost:8000/api/dashboard/snapshot/health"
```

## 📈 Performance

- **Geração de Snapshot**: ~20 segundos (6 KPIs)
- **Resposta da API**: < 20ms
- **Melhoria**: 99.9% mais rápido que o sistema original
- **Disponibilidade**: 24/7 com dados pré-calculados

## 🔍 Monitoramento

### Logs

```bash
# Ver logs de geração
tail -f logs/snapshot_generation.log

# Verificar último snapshot
cat data/snapshots/latest.json | jq '.metadata'
```

### Health Check

```bash
# Status do sistema
curl "http://localhost:8000/api/dashboard/snapshot/health"
```

## 🛠️ Troubleshooting

### Problemas Comuns

1. **Snapshot não gerado**
   - Verificar credenciais do banco
   - Verificar logs em `logs/snapshot_generation.log`
   - Testar conexão manual: `poetry run python scripts/generate_daily_snapshot.py`

2. **API retorna erro**
   - Verificar se feature flag `DASHBOARD_SNAPSHOT=true`
   - Verificar se arquivo `data/snapshots/latest.json` existe
   - Verificar logs do servidor

3. **Performance degradada**
   - Verificar idade do snapshot (deve ser < 25 horas)
   - Regenerar manualmente se necessário
   - Verificar saúde do banco de dados

### Comandos Úteis

```bash
# Forçar regeneração (dev only)
curl "http://localhost:8000/api/dashboard/snapshot?regenerate=true"

# Verificar estrutura do snapshot
cat data/snapshots/latest.json | jq '.summary'

# Testar script manualmente
poetry run python scripts/generate_daily_snapshot.py --verbose
```

## 🔮 Próximos Passos

1. **Alertas**: Implementar notificações para falhas
2. **Métricas**: Adicionar mais KPIs conforme necessário
3. **Multi-cliente**: Expandir para múltiplos clientes
4. **Histórico**: Manter histórico de snapshots
5. **Dashboard**: Interface de monitoramento
