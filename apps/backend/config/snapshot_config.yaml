# DataHero4 Snapshot System Configuration
# =====================================

# System settings
system:
  version: "1.0"
  environment: "production"  # development, staging, production
  
# Critical KPIs configuration
critical_kpis:
  count: 6
  ids:
    - "total_volume"
    - "average_spread" 
    - "conversion_rate"
    - "average_ticket"
    - "retention_rate"
    - "operations_per_analyst"
  
  # Performance thresholds
  calculation_timeout: 30  # seconds
  max_retry_attempts: 3
  
# Snapshot generation settings
generation:
  # Schedule (cron format)
  schedule: "0 3 * * *"  # Daily at 3 AM BRT
  
  # File settings
  snapshot_directory: "data/snapshots"
  keep_history_days: 30
  
  # Performance settings
  batch_size: 6
  parallel_calculation: false
  
# API settings
api:
  cache_ttl: 3600  # 1 hour
  max_response_time_ms: 100
  enable_regeneration: true  # Allow manual regeneration
  
# Health monitoring
monitoring:
  max_age_hours: 25  # Consider stale after 25 hours
  min_success_rate: 80  # Minimum acceptable success rate (%)
  
  # Alert thresholds
  alerts:
    stale_snapshot: 25  # hours
    low_success_rate: 50  # percentage
    generation_failure: true
    
# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/snapshot_generation.log"
  max_size_mb: 100
  backup_count: 5
  
  # Log format
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
# Database settings
database:
  connection_timeout: 30
  query_timeout: 60
  max_retries: 3
  
# Feature flags
features:
  enable_snapshot_system: true
  enable_health_checks: true
  enable_performance_monitoring: true
  enable_error_recovery: true
  
# Client configuration
clients:
  default: "L2M"
  supported:
    - "L2M"
  
# Backup and recovery
backup:
  enable: true
  directory: "data/backups"
  retention_days: 7
  
# Performance targets
performance:
  snapshot_generation_max_time: 60  # seconds
  api_response_max_time: 0.1  # seconds
  success_rate_target: 95  # percentage
