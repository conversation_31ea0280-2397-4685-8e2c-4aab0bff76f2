#!/usr/bin/env python3
"""
KPI Migration Script - DataHero4
================================

Migrates KPI definitions from JSON file to PostgreSQL database.
This script reads the kpis-exchange-json.json file and populates the kpi_definitions table.

Usage:
    python migrate_kpis.py [--dry-run] [--force]
    
Options:
    --dry-run: Show what would be migrated without actually doing it
    --force: Force migration even if table already has data
"""

import json
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any
import logging

# Add the backend src to path
backend_src = Path(__file__).parent.parent
sys.path.insert(0, str(backend_src))

# Set PYTHONPATH to include the src directory
import os
os.environ['PYTHONPATH'] = str(backend_src)

# Load environment variables from backend directory
from dotenv import load_dotenv
backend_dir = Path(__file__).parent.parent.parent
env_path = backend_dir / '.env'
load_dotenv(env_path)

from src.models.learning_models import KpiDefinition, create_all_tables
from src.utils.learning_db_utils import get_db_manager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_kpi_json(json_path: Path) -> Dict[str, Any]:
    """Load KPI definitions from JSON file."""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load JSON file {json_path}: {e}")
        raise


def map_format_type(unit: str) -> str:
    """Map unit to format type for frontend compatibility."""
    if not unit:
        return 'number'
    
    unit_lower = unit.lower()
    if 'r$' in unit_lower or 'us$' in unit_lower or 'monetário' in unit_lower:
        return 'currency'
    elif '%' in unit_lower or 'percentual' in unit_lower:
        return 'percentage'
    else:
        return 'number'


def map_chart_type(category: str, kpi_id: str) -> str:
    """Determine appropriate chart type based on KPI category and ID."""
    # Priority KPIs get area charts for visual emphasis
    priority_kpis = ['total_volume', 'average_ticket', 'conversion_rate', 'retention_rate']
    if kpi_id in priority_kpis:
        return 'area'
    
    # Financial KPIs typically use line charts
    if category in ['financial', 'market']:
        return 'line'
    
    # Operational KPIs can use bar charts
    if category == 'operational':
        return 'bar'
    
    # Default to line
    return 'line'


def is_priority_kpi(kpi_id: str) -> bool:
    """Determine if KPI should be marked as priority."""
    priority_kpis = [
        'total_volume', 'average_ticket', 'conversion_rate', 
        'retention_rate', 'average_spread', 'liquidity_index'
    ]
    return kpi_id in priority_kpis


def convert_kpi_to_model(kpi: Dict[str, Any], category: str, order: int) -> KpiDefinition:
    """Convert JSON KPI definition to database model."""
    
    # Map format type
    format_type = map_format_type(kpi.get('unit', ''))
    
    # Determine chart type
    chart_type = map_chart_type(category, kpi['id'])
    
    # Check if priority
    is_priority = is_priority_kpi(kpi['id'])
    
    # Create alert configuration if needed
    alert_config = None
    if is_priority:
        # Add sample alert configuration for priority KPIs
        if format_type == 'currency':
            alert_config = {
                "type": "above",
                "threshold": 3000000,
                "message": f"{kpi['name']} acima do limite operacional!"
            }
        elif format_type == 'percentage':
            alert_config = {
                "type": "below", 
                "threshold": 90,
                "message": f"{kpi['name']} abaixo do esperado"
            }
    
    return KpiDefinition(
        id=kpi['id'],
        name=kpi['name'],
        description=kpi['description'],
        category=category,
        formula=kpi['formula'],
        unit=kpi.get('unit'),
        format_type=format_type,
        frequency=kpi.get('frequency'),
        importance=kpi.get('importance'),
        sector='cambio',
        is_active=True,
        chart_type=chart_type,
        is_priority=is_priority,
        display_order=order,
        alert_config=alert_config
    )


def migrate_kpis(json_path: Path, dry_run: bool = False, force: bool = False) -> bool:
    """
    Migrate KPIs from JSON to database.
    
    Args:
        json_path: Path to KPI JSON file
        dry_run: If True, only show what would be migrated
        force: If True, migrate even if table has data
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Load JSON data
        logger.info(f"Loading KPI data from {json_path}")
        kpi_data = load_kpi_json(json_path)
        
        # Create database connection directly using main DB credentials
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker

        # Use main database credentials
        db_host = os.getenv('DB_CAMBIO_HOST')
        db_port = os.getenv('DB_CAMBIO_PORT', '5432')
        db_name = os.getenv('DB_CAMBIO_NAME')
        db_user = os.getenv('DB_CAMBIO_USER')
        db_password = os.getenv('DB_CAMBIO_PASSWORD')

        if not all([db_host, db_name, db_user, db_password]):
            logger.error("Missing database credentials. Check DB_CAMBIO_* environment variables.")
            return False

        connection_string = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        logger.info(f"Connecting to database: {db_host}:{db_port}/{db_name}")

        try:
            engine = create_engine(connection_string)
            Session = sessionmaker(bind=engine)
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return False
        
        # Create tables if they don't exist
        logger.info("Creating tables if they don't exist...")
        create_all_tables(engine)

        # Check if table already has data
        session = Session()
        try:
            existing_count = session.query(KpiDefinition).count()
            if existing_count > 0 and not force:
                logger.warning(f"Table already has {existing_count} KPIs. Use --force to overwrite.")
                return False

            if force and existing_count > 0:
                logger.info(f"Clearing existing {existing_count} KPIs...")
                if not dry_run:
                    session.query(KpiDefinition).delete()
                    session.commit()
        finally:
            session.close()
        
        # Process categories and KPIs
        total_kpis = 0
        kpi_models = []
        
        for category in kpi_data['categories']:
            category_id = category['id']
            category_name = category['name']
            
            logger.info(f"Processing category: {category_name} ({category_id})")
            
            for i, kpi in enumerate(category['kpis']):
                order = total_kpis + i
                kpi_model = convert_kpi_to_model(kpi, category_id, order)
                kpi_models.append(kpi_model)
                
                if dry_run:
                    logger.info(f"  Would migrate: {kpi['id']} - {kpi['name']} (order: {order})")
            
            total_kpis += len(category['kpis'])
        
        logger.info(f"Total KPIs to migrate: {total_kpis}")
        
        if dry_run:
            logger.info("DRY RUN - No changes made to database")
            return True
        
        # Insert KPIs into database
        logger.info("Inserting KPIs into database...")
        session = Session()
        try:
            session.add_all(kpi_models)
            session.commit()
        finally:
            session.close()
            
        logger.info(f"Successfully migrated {total_kpis} KPIs to database")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Migrate KPIs from JSON to PostgreSQL')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be migrated without doing it')
    parser.add_argument('--force', action='store_true', help='Force migration even if table has data')
    parser.add_argument('--json-path', type=Path, 
                       default=Path(__file__).parent.parent / 'config' / 'setores' / 'cambio' / 'kpis-exchange-json.json',
                       help='Path to KPI JSON file')
    
    args = parser.parse_args()
    
    if not args.json_path.exists():
        logger.error(f"JSON file not found: {args.json_path}")
        sys.exit(1)
    
    success = migrate_kpis(args.json_path, args.dry_run, args.force)
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
