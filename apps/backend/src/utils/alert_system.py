"""
Alert System for DataHero4 Snapshot Monitoring
==============================================

Sistema de alertas para monitorar falhas e problemas no sistema de snapshots.
"""

import json
import logging
import smtplib
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ult<PERSON>art
from typing import Dict, Any, List, Optional
from enum import Enum
import os

logger = logging.getLogger(__name__)

class AlertLevel(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertType(str, Enum):
    SNAPSHOT_FAILURE = "snapshot_failure"
    STALE_SNAPSHOT = "stale_snapshot"
    LOW_SUCCESS_RATE = "low_success_rate"
    API_ERROR = "api_error"
    HEALTH_CHECK_FAILURE = "health_check_failure"

class AlertManager:
    """Gerenciador de alertas para o sistema de snapshots."""
    
    def __init__(self):
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.smtp_user = os.getenv('SMTP_USER', '')
        self.smtp_password = os.getenv('SMTP_PASSWORD', '')
        self.alert_recipients = os.getenv('ALERT_RECIPIENTS', '').split(',')
        self.webhook_url = os.getenv('SLACK_WEBHOOK_URL', '')
        
    def send_alert(
        self,
        alert_type: AlertType,
        level: AlertLevel,
        title: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """Enviar alerta via múltiplos canais."""
        alert_data = {
            "timestamp": datetime.now().isoformat(),
            "type": alert_type.value,
            "level": level.value,
            "title": title,
            "message": message,
            "details": details or {},
            "system": "datahero4_snapshot"
        }
        
        # Log do alerta
        logger.error(f"🚨 ALERT [{level.value.upper()}]: {title} - {message}")
        
        # Enviar por email se configurado
        if self.smtp_user and self.alert_recipients:
            self._send_email_alert(alert_data)
        
        # Enviar para Slack se configurado
        if self.webhook_url:
            self._send_slack_alert(alert_data)
        
        # Salvar em arquivo para backup
        self._save_alert_to_file(alert_data)
    
    def _send_email_alert(self, alert_data: Dict[str, Any]):
        """Enviar alerta por email."""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.smtp_user
            msg['To'] = ', '.join(self.alert_recipients)
            msg['Subject'] = f"[DataHero4] {alert_data['level'].upper()}: {alert_data['title']}"
            
            # Corpo do email
            body = f"""
DataHero4 Snapshot System Alert

Level: {alert_data['level'].upper()}
Type: {alert_data['type']}
Time: {alert_data['timestamp']}

Message: {alert_data['message']}

Details:
{json.dumps(alert_data['details'], indent=2)}

---
DataHero4 Monitoring System
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Enviar email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_user, self.smtp_password)
            server.send_message(msg)
            server.quit()
            
            logger.info("📧 Alert sent via email")
            
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
    
    def _send_slack_alert(self, alert_data: Dict[str, Any]):
        """Enviar alerta para Slack."""
        try:
            import requests
            
            # Emoji baseado no nível
            emoji_map = {
                "info": "ℹ️",
                "warning": "⚠️", 
                "error": "❌",
                "critical": "🚨"
            }
            
            emoji = emoji_map.get(alert_data['level'], "🔔")
            
            payload = {
                "text": f"{emoji} DataHero4 Alert",
                "attachments": [
                    {
                        "color": "danger" if alert_data['level'] in ['error', 'critical'] else "warning",
                        "fields": [
                            {
                                "title": "Level",
                                "value": alert_data['level'].upper(),
                                "short": True
                            },
                            {
                                "title": "Type", 
                                "value": alert_data['type'],
                                "short": True
                            },
                            {
                                "title": "Message",
                                "value": alert_data['message'],
                                "short": False
                            }
                        ],
                        "footer": "DataHero4 Monitoring",
                        "ts": int(datetime.now().timestamp())
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=payload)
            response.raise_for_status()
            
            logger.info("💬 Alert sent to Slack")
            
        except Exception as e:
            logger.error(f"Failed to send Slack alert: {e}")
    
    def _save_alert_to_file(self, alert_data: Dict[str, Any]):
        """Salvar alerta em arquivo para backup."""
        try:
            alerts_dir = "logs/alerts"
            os.makedirs(alerts_dir, exist_ok=True)
            
            date_str = datetime.now().strftime("%Y%m%d")
            alert_file = f"{alerts_dir}/alerts_{date_str}.jsonl"
            
            with open(alert_file, 'a') as f:
                f.write(json.dumps(alert_data) + '\n')
                
        except Exception as e:
            logger.error(f"Failed to save alert to file: {e}")

# Instância global
alert_manager = AlertManager()

# Funções de conveniência
def alert_snapshot_failure(client_id: str, error: str, duration: Optional[float] = None):
    """Alerta para falha na geração de snapshot."""
    alert_manager.send_alert(
        AlertType.SNAPSHOT_FAILURE,
        AlertLevel.CRITICAL,
        "Snapshot Generation Failed",
        f"Failed to generate snapshot for client {client_id}",
        {
            "client_id": client_id,
            "error": error,
            "duration_seconds": duration
        }
    )

def alert_stale_snapshot(age_hours: float, client_id: str = "L2M"):
    """Alerta para snapshot desatualizado."""
    alert_manager.send_alert(
        AlertType.STALE_SNAPSHOT,
        AlertLevel.WARNING,
        "Stale Snapshot Detected",
        f"Snapshot is {age_hours:.1f} hours old",
        {
            "client_id": client_id,
            "age_hours": age_hours,
            "threshold_hours": 25
        }
    )

def alert_low_success_rate(success_rate: float, client_id: str = "L2M"):
    """Alerta para baixa taxa de sucesso."""
    alert_manager.send_alert(
        AlertType.LOW_SUCCESS_RATE,
        AlertLevel.ERROR,
        "Low KPI Success Rate",
        f"KPI calculation success rate is {success_rate:.1f}%",
        {
            "client_id": client_id,
            "success_rate": success_rate,
            "threshold": 80
        }
    )

def alert_api_error(endpoint: str, error: str, client_id: str = "L2M"):
    """Alerta para erro na API."""
    alert_manager.send_alert(
        AlertType.API_ERROR,
        AlertLevel.ERROR,
        "API Error",
        f"Error in {endpoint}: {error}",
        {
            "endpoint": endpoint,
            "client_id": client_id,
            "error": error
        }
    )
