"""
Structured Logging Utilities for DataHero4
==========================================

Provides structured logging with JSON format for better monitoring and alerting.
"""

import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from enum import Enum

class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class SnapshotEvent(str, Enum):
    GENERATION_START = "snapshot_generation_start"
    GENERATION_SUCCESS = "snapshot_generation_success"
    GENERATION_FAILURE = "snapshot_generation_failure"
    KPI_CALCULATION_START = "kpi_calculation_start"
    KPI_CALCULATION_SUCCESS = "kpi_calculation_success"
    KPI_CALCULATION_FAILURE = "kpi_calculation_failure"
    API_REQUEST = "api_request"
    API_RESPONSE = "api_response"
    HEALTH_CHECK = "health_check"

class StructuredLogger:
    """Structured logger for snapshot system events."""
    
    def __init__(self, name: str = "snapshot_system"):
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """Setup structured logging with JSON formatter."""
        if self.logger.handlers:
            return  # Already configured
            
        # Create formatter
        formatter = StructuredFormatter()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_dir / "structured_logs.jsonl")
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        self.logger.setLevel(logging.INFO)
    
    def log_event(
        self,
        event: SnapshotEvent,
        level: LogLevel = LogLevel.INFO,
        message: str = "",
        **kwargs
    ):
        """Log a structured event."""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "event": event.value,
            "message": message,
            "system": "snapshot_system",
            **kwargs
        }
        
        # Log with appropriate level
        getattr(self.logger, level.value.lower())(json.dumps(log_data))
    
    def log_snapshot_generation_start(self, client_id: str):
        """Log snapshot generation start."""
        self.log_event(
            SnapshotEvent.GENERATION_START,
            LogLevel.INFO,
            f"Starting snapshot generation for client {client_id}",
            client_id=client_id
        )
    
    def log_snapshot_generation_success(
        self, 
        client_id: str, 
        kpi_count: int, 
        success_rate: float,
        duration_seconds: float
    ):
        """Log successful snapshot generation."""
        self.log_event(
            SnapshotEvent.GENERATION_SUCCESS,
            LogLevel.INFO,
            f"Snapshot generated successfully for {client_id}",
            client_id=client_id,
            kpi_count=kpi_count,
            success_rate=success_rate,
            duration_seconds=duration_seconds,
            performance_status="good" if duration_seconds < 60 else "slow"
        )
    
    def log_snapshot_generation_failure(
        self, 
        client_id: str, 
        error: str,
        duration_seconds: Optional[float] = None
    ):
        """Log failed snapshot generation."""
        self.log_event(
            SnapshotEvent.GENERATION_FAILURE,
            LogLevel.ERROR,
            f"Snapshot generation failed for {client_id}: {error}",
            client_id=client_id,
            error=error,
            duration_seconds=duration_seconds,
            alert_required=True
        )
    
    def log_kpi_calculation(
        self, 
        kpi_id: str, 
        success: bool, 
        value: Optional[float] = None,
        duration_seconds: Optional[float] = None,
        error: Optional[str] = None
    ):
        """Log KPI calculation result."""
        event = SnapshotEvent.KPI_CALCULATION_SUCCESS if success else SnapshotEvent.KPI_CALCULATION_FAILURE
        level = LogLevel.INFO if success else LogLevel.WARNING
        
        log_data = {
            "kpi_id": kpi_id,
            "success": success,
            "duration_seconds": duration_seconds
        }
        
        if success and value is not None:
            log_data["value"] = value
            message = f"KPI {kpi_id} calculated successfully: {value}"
        else:
            log_data["error"] = error
            message = f"KPI {kpi_id} calculation failed: {error}"
        
        self.log_event(event, level, message, **log_data)
    
    def log_api_request(
        self, 
        endpoint: str, 
        client_id: str,
        response_time_ms: Optional[float] = None,
        status_code: Optional[int] = None
    ):
        """Log API request."""
        self.log_event(
            SnapshotEvent.API_REQUEST,
            LogLevel.INFO,
            f"API request to {endpoint}",
            endpoint=endpoint,
            client_id=client_id,
            response_time_ms=response_time_ms,
            status_code=status_code,
            performance_status="good" if response_time_ms and response_time_ms < 100 else "slow"
        )
    
    def log_health_check(
        self, 
        status: str, 
        success_rate: float,
        age_hours: float,
        kpi_count: int
    ):
        """Log health check result."""
        level = LogLevel.INFO if status == "healthy" else LogLevel.WARNING
        
        self.log_event(
            SnapshotEvent.HEALTH_CHECK,
            level,
            f"Health check: {status}",
            health_status=status,
            success_rate=success_rate,
            age_hours=age_hours,
            kpi_count=kpi_count,
            alert_required=status in ["unhealthy", "stale"]
        )

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logs."""
    
    def format(self, record):
        # If the message is already JSON, return as-is
        try:
            json.loads(record.getMessage())
            return record.getMessage()
        except (json.JSONDecodeError, ValueError):
            # If not JSON, wrap in structured format
            log_data = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "level": record.levelname,
                "logger": record.name,
                "message": record.getMessage(),
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno
            }
            
            if record.exc_info:
                log_data["exception"] = self.formatException(record.exc_info)
            
            return json.dumps(log_data)

# Global structured logger instance
structured_logger = StructuredLogger()

# Convenience functions
def log_snapshot_start(client_id: str):
    structured_logger.log_snapshot_generation_start(client_id)

def log_snapshot_success(client_id: str, kpi_count: int, success_rate: float, duration: float):
    structured_logger.log_snapshot_generation_success(client_id, kpi_count, success_rate, duration)

def log_snapshot_failure(client_id: str, error: str, duration: Optional[float] = None):
    structured_logger.log_snapshot_generation_failure(client_id, error, duration)

def log_kpi_result(kpi_id: str, success: bool, **kwargs):
    structured_logger.log_kpi_calculation(kpi_id, success, **kwargs)

def log_api_call(endpoint: str, client_id: str, **kwargs):
    structured_logger.log_api_request(endpoint, client_id, **kwargs)

def log_health_status(status: str, **kwargs):
    structured_logger.log_health_check(status, **kwargs)
