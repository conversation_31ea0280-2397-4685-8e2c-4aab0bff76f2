#!/usr/bin/env python3
"""
CLI Interativa Aprimorada para DataHero4
========================================

Versão que integra com APIs reais quando disponíveis, mas funciona
standalone quando necessário. Demonstra classificação de entrada
real usando LLMs.

Uso:
    poetry run python -m src.cli.enhanced_chat_cli
    poetry run python -m src.cli.enhanced_chat_cli --debug --use-real-apis
"""

import asyncio
import os
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
from rich.prompt import Prompt
from rich import box
from dotenv import load_dotenv

# IMPORTANTE: Carregar variáveis de ambiente ANTES de verificar APIs
load_dotenv()

# Importar configuração centralizada de LLMs
from src.config.llm_config import (
    get_llm_config,
    LLMStage,
    LLMProvider,
    get_model_for_stage,
    is_llm_available
)

# Verificar disponibilidade das APIs baseado na configuração
llm_config = get_llm_config()
REAL_APIS_AVAILABLE = len(llm_config.get_available_providers()) > 0

if REAL_APIS_AVAILABLE:
    print(f"✅ APIs disponíveis: {[p.value for p in llm_config.get_available_providers()]}")
else:
    print("⚠️ Nenhuma API LLM disponível - usando modo simulação")

# Suprimir warnings desnecessários
import warnings
import urllib3
warnings.filterwarnings("ignore", category=UserWarning, module="urllib3")
urllib3.disable_warnings(urllib3.exceptions.NotOpenSSLWarning)

console = Console()

@dataclass
class MessageClassification:
    """Resultado da classificação de uma mensagem."""
    message_type: str  # "new_question", "follow_up", "feedback"
    confidence: float
    reasoning: str
    context_applied: bool = False
    inherited_context: Dict[str, Any] = field(default_factory=dict)
    feedback_details: Optional[Dict[str, Any]] = None
    processing_time: float = 0.0

@dataclass
class ChatSession:
    """Representa uma sessão de chat ativa."""
    thread_id: str
    user_id: str
    client_id: str
    sector: str
    created_at: datetime
    message_count: int = 0
    debug_mode: bool = False
    verbose_level: int = 1
    use_real_apis: bool = False
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    context_memory: Dict[str, Any] = field(default_factory=dict)

class EnhancedChatCLI:
    """CLI interativa aprimorada para DataHero4."""
    
    def __init__(self, use_real_apis: bool = False):
        self.current_session: Optional[ChatSession] = None
        self.sessions_history: List[ChatSession] = []
        self.use_real_apis = use_real_apis and REAL_APIS_AVAILABLE
        
        # Inicializar clientes de API baseado na configuração centralizada
        self.api_clients = {}

        if self.use_real_apis:
            self._setup_api_clients()

    def _setup_api_clients(self):
        """Configura clientes de API baseado na configuração centralizada."""
        try:
            # Configurar apenas Fireworks (como solicitado)
            if llm_config.is_provider_available(LLMProvider.FIREWORKS):
                from openai import OpenAI
                api_key = llm_config.get_api_key(LLMProvider.FIREWORKS)
                self.api_clients[LLMProvider.FIREWORKS] = OpenAI(
                    api_key=api_key,
                    base_url="https://api.fireworks.ai/inference/v1"
                )
                console.print("[green]✅ Fireworks AI configurada[/green]")
            else:
                console.print("[red]❌ Fireworks API key não encontrada[/red]")
                self.use_real_apis = False
                return

            # Verificar se a API foi configurada com sucesso
            if LLMProvider.FIREWORKS in self.api_clients:
                console.print(f"[green]🔌 Usando apenas Fireworks AI[/green]")
                self.use_real_apis = True
            else:
                console.print("[yellow]⚠️ Falha ao configurar Fireworks, usando simulação[/yellow]")
                self.use_real_apis = False

        except Exception as e:
            console.print(f"[red]❌ Erro ao configurar Fireworks API: {e}[/red]")
            console.print("[yellow]🔄 Voltando para modo simulação[/yellow]")
            self.use_real_apis = False

    def create_new_session(self, user_id: str, client_id: str, sector: str, debug_mode: bool = False) -> ChatSession:
        """Cria uma nova sessão de chat."""
        thread_id = f"enhanced-{uuid.uuid4().hex[:8]}-{int(time.time())}"
        
        session = ChatSession(
            thread_id=thread_id,
            user_id=user_id,
            client_id=client_id,
            sector=sector,
            created_at=datetime.now(),
            debug_mode=debug_mode,
            use_real_apis=self.use_real_apis
        )
        
        self.current_session = session
        self.sessions_history.append(session)
        
        return session
    
    def display_welcome(self, session: ChatSession):
        """Exibe mensagem de boas-vindas."""
        api_status = "🟢 APIs Reais" if session.use_real_apis else "🟡 Simulação"
        
        welcome_panel = Panel(
            f"""[bold green]🤖 DataHero4 Enhanced Chat CLI[/bold green]

[bold]Sessão Ativa:[/bold]
• Thread ID: [cyan]{session.thread_id}[/cyan]
• Cliente: [yellow]{session.client_id}[/yellow]
• Setor: [yellow]{session.sector}[/yellow]
• Debug Mode: [{'green' if session.debug_mode else 'red'}]{session.debug_mode}[/]
• APIs: {api_status}

[bold]Comandos Especiais:[/bold]
• [cyan]/new[/cyan] - Nova thread
• [cyan]/history[/cyan] - Histórico da conversa
• [cyan]/context[/cyan] - Contexto preservado
• [cyan]/debug[/cyan] - Alternar modo debug
• [cyan]/quiet[/cyan] - Alternar modo quiet (apenas respostas)
• [cyan]/apis[/cyan] ou [cyan]/api[/cyan] - Alternar uso de APIs reais
• [cyan]/help[/cyan] - Ajuda
• [cyan]/exit[/cyan] - Sair

[bold]Funcionalidades:[/bold]
• ✅ Classificação com LLMs reais (quando disponível)
• ✅ Preservação de contexto conversacional
• ✅ Detecção inteligente de feedback
• ✅ Interface rica com métricas de performance

[dim]Digite sua pergunta ou use um comando especial...[/dim]""",
            title="🚀 DataHero4 Enhanced CLI",
            border_style="blue",
            padding=(1, 2)
        )
        
        console.print(welcome_panel)
        console.print()
    
    async def classify_message_with_llm(self, message: str, session: ChatSession) -> MessageClassification:
        """Classifica mensagem usando LLM real."""
        start_time = time.time()
        
        try:
            # Construir contexto da conversa
            context = ""
            if session.conversation_history:
                last_messages = session.conversation_history[-3:]
                for entry in last_messages:
                    context += f"User: {entry['user_message']}\nAssistant: {entry['assistant_response'][:100]}...\n\n"
            
            # Prompt para classificação
            classification_prompt = f"""Analise a seguinte mensagem do usuário e classifique-a em uma das categorias:

1. "new_question" - Nova pergunta independente
2. "follow_up" - Pergunta que faz referência à conversa anterior
3. "feedback" - Feedback sobre resposta anterior (positivo, negativo ou correção)

Contexto da conversa anterior:
{context}

Mensagem atual do usuário: "{message}"

Responda APENAS com um JSON no formato:
{{
    "message_type": "new_question|follow_up|feedback",
    "confidence": 0.0-1.0,
    "reasoning": "explicação breve",
    "feedback_type": "positive|negative|correction" (apenas se for feedback)
}}"""

            # Usar configuração centralizada para classificação
            model_config = get_model_for_stage(LLMStage.MESSAGE_CLASSIFICATION)
            provider = model_config.provider

            if provider in self.api_clients:
                client = self.api_clients[provider]

                if provider == LLMProvider.ANTHROPIC:
                    response = client.messages.create(
                        model=model_config.model_name,
                        max_tokens=model_config.max_tokens,
                        temperature=model_config.temperature,
                        messages=[{"role": "user", "content": classification_prompt}]
                    )
                    result_text = response.content[0].text

                elif provider in [LLMProvider.FIREWORKS, LLMProvider.OPENAI]:
                    response = client.chat.completions.create(
                        model=model_config.model_name,
                        max_tokens=model_config.max_tokens,
                        temperature=model_config.temperature,
                        messages=[{"role": "user", "content": classification_prompt}]
                    )
                    result_text = response.choices[0].message.content

                elif provider == LLMProvider.TOGETHER:
                    response = client.chat.completions.create(
                        model=model_config.model_name,
                        messages=[{"role": "user", "content": classification_prompt}],
                        max_tokens=model_config.max_tokens,
                        temperature=model_config.temperature
                    )
                    result_text = response.choices[0].message.content
                else:
                    raise ValueError(f"Provider não suportado: {provider}")
            
            else:
                # Fallback para classificação heurística
                return await self.classify_message_heuristic(message, session)
            
            # Parsear resposta JSON
            import json
            try:
                result = json.loads(result_text.strip())
                
                feedback_details = None
                if result.get("message_type") == "feedback":
                    feedback_details = {
                        "intent_type": result.get("feedback_type", "general"),
                        "should_reprocess": result.get("feedback_type") == "correction"
                    }
                
                return MessageClassification(
                    message_type=result.get("message_type", "new_question"),
                    confidence=float(result.get("confidence", 0.8)),
                    reasoning=f"LLM: {result.get('reasoning', 'Classificação automática')}",
                    feedback_details=feedback_details,
                    processing_time=time.time() - start_time
                )
                
            except (json.JSONDecodeError, KeyError) as e:
                console.print(f"[yellow]⚠️ Erro ao parsear resposta LLM: {e}[/yellow]")
                return await self.classify_message_heuristic(message, session)
                
        except Exception as e:
            console.print(f"[yellow]⚠️ Erro na API: {e}. Usando classificação heurística.[/yellow]")
            return await self.classify_message_heuristic(message, session)
    
    async def classify_message_heuristic(self, message: str, session: ChatSession) -> MessageClassification:
        """Classificação heurística como fallback."""
        start_time = time.time()
        
        # Simular delay de processamento
        await asyncio.sleep(0.1)
        
        message_lower = message.lower()
        
        # Primeira mensagem da conversa
        if session.message_count == 0:
            return MessageClassification(
                message_type="new_question",
                confidence=1.0,
                reasoning="Primeira mensagem da conversa",
                processing_time=time.time() - start_time
            )
        
        # Detectar feedback usando palavras-chave
        feedback_keywords = [
            "errado", "incorreto", "erro", "problema", "ruim", "péssimo",
            "obrigado", "obrigada", "perfeito", "ótimo", "excelente", "bom",
            "não gostei", "adorei", "parabéns", "correto", "certo"
        ]
        
        if any(keyword in message_lower for keyword in feedback_keywords):
            # Determinar tipo de feedback
            positive_keywords = ["obrigado", "obrigada", "perfeito", "ótimo", "excelente", "bom", "adorei", "parabéns", "correto", "certo"]
            negative_keywords = ["errado", "incorreto", "erro", "problema", "ruim", "péssimo", "não gostei"]
            
            if any(keyword in message_lower for keyword in positive_keywords):
                intent_type = "positive_feedback"
            elif any(keyword in message_lower for keyword in negative_keywords):
                intent_type = "negative_correction"
            else:
                intent_type = "general_feedback"
            
            return MessageClassification(
                message_type="feedback",
                confidence=0.85,
                reasoning=f"Heurística: Detectadas palavras-chave de feedback: {intent_type}",
                feedback_details={
                    "intent_type": intent_type,
                    "should_reprocess": intent_type == "negative_correction"
                },
                processing_time=time.time() - start_time
            )
        
        # Detectar follow-up usando palavras-chave
        followup_keywords = [
            "e em", "e para", "e no", "e na", "também", "além disso",
            "comparar com", "e se", "mas e", "e quanto", "e sobre"
        ]
        
        if any(keyword in message_lower for keyword in followup_keywords):
            # Simular contexto herdado
            inherited_context = {
                "temporal_filters": session.context_memory.get("last_temporal", {}),
                "business_filters": session.context_memory.get("last_business", {}),
                "active_conditions": session.context_memory.get("last_conditions", {})
            }
            
            return MessageClassification(
                message_type="follow_up",
                confidence=0.75,
                reasoning="Heurística: Detectadas palavras-chave de follow-up",
                context_applied=True,
                inherited_context=inherited_context,
                processing_time=time.time() - start_time
            )
        
        # Nova questão independente
        return MessageClassification(
            message_type="new_question",
            confidence=0.8,
            reasoning="Heurística: Questão independente sem contexto aplicável",
            processing_time=time.time() - start_time
        )

    async def classify_message(self, message: str, session: ChatSession) -> MessageClassification:
        """Classifica mensagem usando LLM real ou heurística baseado na configuração."""
        # Verificar se deve usar LLM real baseado na configuração
        if session.use_real_apis and is_llm_available(LLMStage.MESSAGE_CLASSIFICATION):
            return await self.classify_message_with_llm(message, session)
        else:
            return await self.classify_message_heuristic(message, session)

    def display_classification(self, classification: MessageClassification, session: ChatSession):
        """Exibe o resultado da classificação da mensagem de forma amigável."""
        # Mostrar classificação por padrão (exceto em modo quiet)
        if session.verbose_level == 0:  # Modo quiet - não mostrar
            return

        # Mapear tipos para exibição amigável
        type_display_map = {
            "new_question": "Nova Pergunta",
            "follow_up": "Pergunta de Acompanhamento",
            "feedback": "Feedback"
        }

        icon_map = {
            "new_question": "🆕",
            "follow_up": "🔄",
            "feedback": "💭"
        }

        color_map = {
            "new_question": "bright_blue",
            "follow_up": "bright_green",
            "feedback": "bright_yellow"
        }

        # Obter valores formatados
        type_display = type_display_map.get(classification.message_type, classification.message_type)
        icon = icon_map.get(classification.message_type, "❓")
        color = color_map.get(classification.message_type, "white")

        # Formatação amigável da confiança
        confidence_percent = f"{classification.confidence*100:.0f}%"
        confidence_emoji = "🎯" if classification.confidence >= 0.9 else "⚡" if classification.confidence >= 0.7 else "⚠️"

        # Método simplificado
        method = "🧠 LLM" if "LLM:" in classification.reasoning else "🔧 Heurística"

        # Tempo formatado
        time_ms = f"{classification.processing_time*1000:.0f}ms"

        # Conteúdo principal (mais limpo)
        classification_content = f"""[bold]{icon} Tipo:[/bold] [{color}]{type_display}[/{color}]
[bold]{confidence_emoji} Confiança:[/bold] {confidence_percent}
[bold]⏱️ Tempo:[/bold] {time_ms}
[bold]🔬 Método:[/bold] {method}"""

        # Adicionar razão apenas se for informativa (não técnica demais)
        if not classification.reasoning.startswith("Heurística:"):
            classification_content += f"\n[bold]💡 Razão:[/bold] {classification.reasoning}"

        # Informações extras apenas se relevantes
        if classification.context_applied:
            classification_content += f"\n[bold]🧠 Contexto:[/bold] [green]✅ Aplicado[/green]"

        if classification.feedback_details:
            details = classification.feedback_details
            feedback_type = details.get('intent_type', 'N/A')
            classification_content += f"\n[bold]📝 Feedback:[/bold] {feedback_type}"

        panel = Panel(
            classification_content,
            title="🔍 Classificação da Mensagem",
            border_style=color,
            padding=(0, 1)
        )

        console.print(panel)

    async def handle_special_command(self, command: str, session: ChatSession) -> bool:
        """Processa comandos especiais. Retorna True se deve continuar o loop."""
        command = command.lower().strip()

        if command == "/exit":
            console.print("[bold red]👋 Encerrando sessão...[/bold red]")
            return False

        elif command == "/new":
            console.print("[bold blue]🆕 Criando nova sessão...[/bold blue]")
            new_session = self.create_new_session(
                user_id=session.user_id,
                client_id=session.client_id,
                sector=session.sector,
                debug_mode=session.debug_mode
            )
            self.display_welcome(new_session)
            return True

        elif command == "/debug":
            session.debug_mode = not session.debug_mode
            status = "[green]ativado[/green]" if session.debug_mode else "[red]desativado[/red]"
            console.print(f"[bold]🐛 Modo debug {status}[/bold]")
            return True

        elif command == "/quiet":
            # Alternar entre modo quiet (0) e normal (1)
            session.verbose_level = 0 if session.verbose_level > 0 else 1
            if session.verbose_level == 0:
                console.print("[bold]🔇 Modo quiet ativado - apenas respostas[/bold]")
            else:
                console.print("[bold]🔊 Modo normal ativado - com indicadores de progresso[/bold]")
            return True

        elif command in ["/apis", "/api"]:
            if REAL_APIS_AVAILABLE:
                session.use_real_apis = not session.use_real_apis
                status = "[green]ativadas[/green]" if session.use_real_apis else "[yellow]simulação[/yellow]"
                console.print(f"[bold]🔌 APIs reais {status}[/bold]")
            else:
                console.print("[red]❌ APIs reais não disponíveis (dependências não instaladas)[/red]")
            return True

        elif command == "/verbose":
            session.verbose_level = (session.verbose_level % 3) + 1
            levels = {1: "Normal", 2: "Verbose", 3: "Debug"}
            console.print(f"[bold]📢 Verbosidade: {levels[session.verbose_level]}[/bold]")
            return True

        elif command == "/history":
            self.show_conversation_history(session)
            return True

        elif command == "/context":
            self.show_preserved_context(session)
            return True

        elif command == "/help":
            self.show_help()
            return True

        else:
            console.print(f"[red]❌ Comando desconhecido: {command}[/red]")
            console.print("[dim]Digite /help para ver comandos disponíveis[/dim]")
            return True

    def show_help(self):
        """Exibe ajuda sobre comandos disponíveis."""
        # Obter status das APIs da configuração centralizada
        available_providers = llm_config.get_available_providers()
        api_status = "🟢 Disponíveis" if available_providers else "🔴 Não disponíveis"

        # Mostrar configuração por etapa
        stage_summary = llm_config.get_stage_summary()

        # Construir lista de provedores
        provider_status = []
        for provider in LLMProvider:
            status = "✅" if provider in available_providers else "❌"
            provider_status.append(f"• {provider.value.title()}: {status}")

        # Mostrar configuração de etapas principais
        classification_config = stage_summary.get(LLMStage.MESSAGE_CLASSIFICATION.value, {})
        analysis_config = stage_summary.get(LLMStage.RESULT_ANALYSIS.value, {})

        help_content = f"""[bold]🆘 Comandos Disponíveis:[/bold]

[bold cyan]/new[/bold cyan] - Criar nova sessão de chat
[bold cyan]/history[/bold cyan] - Mostrar histórico da conversa atual
[bold cyan]/context[/bold cyan] - Exibir contexto preservado
[bold cyan]/debug[/bold cyan] - Alternar modo debug (logs detalhados)
[bold cyan]/quiet[/bold cyan] - Alternar modo quiet (apenas respostas)
[bold cyan]/apis[/bold cyan] - Alternar uso de APIs reais vs simulação
[bold cyan]/verbose[/bold cyan] - Alternar nível de verbosidade (1-3)
[bold cyan]/help[/bold cyan] - Mostrar esta ajuda
[bold cyan]/exit[/bold cyan] - Sair da CLI

[bold]🔌 Status das APIs:[/bold] {api_status}
{chr(10).join(provider_status)}

[bold]⚙️ Configuração de LLMs:[/bold]
• Classificação: {classification_config.get('provider', 'N/A')} ({'✅' if classification_config.get('available') else '❌'})
• Análise: {analysis_config.get('provider', 'N/A')} ({'✅' if analysis_config.get('available') else '❌'})

[bold]💡 Dicas:[/bold]
• Use /apis para alternar entre LLM real e simulação
• Modo debug mostra método de classificação usado
• APIs reais oferecem classificação mais precisa
• Fallback automático para heurística se APIs falharem
• Configure via variáveis de ambiente (ver .env.example)

[bold]🧪 Exemplos de Teste:[/bold]
1. "Qual volume USD hoje?" (nova questão)
2. "E em março?" (follow-up com contexto temporal)
3. "Isso está errado" (feedback negativo)
4. "Obrigado, perfeito!" (feedback positivo)"""

        panel = Panel(
            help_content,
            title="🆘 Ajuda - DataHero4 Enhanced CLI",
            border_style="blue",
            padding=(1, 2)
        )

        console.print(panel)

    def show_conversation_history(self, session: ChatSession):
        """Exibe o histórico da conversa atual."""
        if not session.conversation_history:
            console.print("[yellow]📭 Nenhuma mensagem no histórico[/yellow]")
            return

        table = Table(title=f"📜 Histórico da Conversa - {session.thread_id}", box=box.ROUNDED)
        table.add_column("Timestamp", style="dim", width=20)
        table.add_column("Tipo", style="bold", width=15)
        table.add_column("Mensagem", style="white", width=50)
        table.add_column("Método", style="cyan", width=10)

        for entry in session.conversation_history[-10:]:  # Últimas 10 mensagens
            timestamp = entry["timestamp"][:19].replace("T", " ")
            classification = entry["classification"].replace("_", " ").title()
            message = entry["user_message"][:50] + "..." if len(entry["user_message"]) > 50 else entry["user_message"]
            method = "LLM" if entry.get("used_llm", False) else "Heurística"

            table.add_row(timestamp, classification, message, method)

        console.print(table)
        console.print(f"[dim]Total de mensagens: {len(session.conversation_history)}[/dim]")

    def show_preserved_context(self, session: ChatSession):
        """Exibe o contexto preservado atual."""
        if not session.context_memory:
            console.print("[yellow]📭 Nenhum contexto preservado[/yellow]")
            return

        table = Table(title="🧠 Contexto Preservado", box=box.ROUNDED)
        table.add_column("Categoria", style="bold cyan", width=20)
        table.add_column("Chave", style="yellow", width=15)
        table.add_column("Valor", style="white", width=30)

        for category, items in session.context_memory.items():
            if isinstance(items, dict):
                for key, value in items.items():
                    table.add_row(category, key, str(value))
            else:
                table.add_row(category, "-", str(items))

        console.print(table)

    async def run_interactive_loop(self, session: ChatSession):
        """Loop principal da CLI interativa."""
        self.display_welcome(session)

        while True:
            try:
                # Prompt para entrada do usuário
                user_input = Prompt.ask(
                    f"\n[bold blue]💬 {session.user_id}[/bold blue]",
                    default=""
                ).strip()

                if not user_input:
                    continue

                # Verificar se é comando especial
                if user_input.startswith("/"):
                    should_continue = await self.handle_special_command(user_input, session)
                    if not should_continue:
                        break
                    continue

                # Processar mensagem normal
                start_time = time.time()

                # 1. Classificar mensagem
                classification = await self.classify_message(user_input, session)
                self.display_classification(classification, session)

                # 2. Processar com indicadores amigáveis
                if session.verbose_level > 0:
                    console.print(f"\n[bold blue]🤖 Processando...[/bold blue]")

                # Processar com pipeline real
                is_followup = classification.message_type == "follow_up"
                response = self._process_with_datahero_pipeline(user_input, session, is_followup)

                # 3. Exibir resposta
                self.display_response(response, session)

                # 4. Atualizar histórico
                session.conversation_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "user_message": user_input,
                    "assistant_response": response,
                    "classification": classification.message_type,
                    "confidence": classification.confidence,
                    "used_llm": "LLM:" in classification.reasoning
                })

                # 5. Atualizar métricas
                processing_time = time.time() - start_time
                session.message_count += 1

                if session.debug_mode:
                    self.display_performance_metrics(processing_time, classification, session)

            except KeyboardInterrupt:
                console.print("\n[bold red]🛑 Interrompido pelo usuário[/bold red]")
                break
            except Exception as e:
                console.print(f"[red]❌ Erro inesperado: {e}[/red]")

        # Exibir estatísticas finais
        self.display_session_summary(session)

    def generate_real_response(self, message: str, classification: MessageClassification, session: ChatSession) -> str:
        """Gera uma resposta real usando o pipeline DataHero4."""

        if classification.message_type == "feedback":
            if classification.feedback_details and classification.feedback_details.get("intent_type") == "positive_feedback":
                return "😊 Fico feliz que tenha gostado! Estou sempre aqui para ajudar com suas análises de dados."
            elif classification.feedback_details and classification.feedback_details.get("intent_type") == "negative_correction":
                # Implementar reprocessamento automático para correções
                return self._handle_negative_correction(message, session)
            else:
                return "📝 Feedback recebido! Suas observações são importantes para melhorar minhas respostas."

        elif classification.message_type == "follow_up":
            # Para follow-ups, usar contexto da conversa anterior
            return self._process_with_datahero_pipeline(message, session, is_followup=True)

        else:  # new_question
            # Para novas perguntas, processar com pipeline completo
            return self._process_with_datahero_pipeline(message, session, is_followup=False)

    def _handle_negative_correction(self, feedback_message: str, session: ChatSession) -> str:
        """Processa feedback negativo e reprocessa automaticamente a pergunta anterior com correção."""
        try:
            # Verificar se há uma pergunta anterior no histórico
            if not session.conversation_history:
                return "🔧 Entendi o feedback, mas não encontrei uma pergunta anterior para corrigir."
            
            # Pegar a última pergunta do histórico
            last_entry = session.conversation_history[-1]
            original_question = last_entry.get("message", "")
            
            if not original_question:
                return "🔧 Entendi o feedback, mas não consegui recuperar a pergunta anterior."
            
            # Extrair a correção do feedback usando heurística simples
            corrected_question = self._extract_correction_from_feedback(feedback_message, original_question)
            
            console.print(f"[yellow]🔄 Reprocessando pergunta com correção:[/yellow]")
            console.print(f"[dim]Original: {original_question}[/dim]")
            console.print(f"[green]Corrigida: {corrected_question}[/green]")
            
            # Reprocessar a pergunta corrigida
            corrected_response = self._process_with_datahero_pipeline(corrected_question, session, is_followup=False)
            
            # Salvar a correção no contexto para futuras referências
            session.context_memory["last_correction"] = {
                "original_question": original_question,
                "corrected_question": corrected_question,
                "feedback": feedback_message
            }
            
            return f"🔧 **Correção Aplicada**\n\n{corrected_response}"
            
        except Exception as e:
            console.print(f"[red]❌ Erro no reprocessamento: {e}[/red]")
            return f"🔧 Entendi o problema, mas ocorreu um erro ao reprocessar: {str(e)}\n\nPor favor, reformule sua pergunta."

    def _extract_correction_from_feedback(self, feedback: str, original_question: str) -> str:
        """Extrai a correção do feedback e aplica à pergunta original."""
        feedback_lower = feedback.lower()
        
        # Detectar correções de moeda
        currency_corrections = {
            "usd": ["usd", "dólar", "dolar", "dollar"],
            "eur": ["eur", "euro"],
            "gbp": ["gbp", "libra", "pound"],
            "brl": ["brl", "real", "reais"]
        }
        
        # Procurar por menções de moedas no feedback
        for currency, keywords in currency_corrections.items():
            if any(keyword in feedback_lower for keyword in keywords):
                # Aplicar correção de moeda
                corrected = f"{original_question} em {currency.upper()}"
                return corrected
        
        # Detectar correções de período
        period_corrections = {
            "janeiro": ["janeiro", "jan"],
            "fevereiro": ["fevereiro", "fev"],
            "março": ["março", "mar"],
            "abril": ["abril", "abr"],
            "maio": ["maio", "mai"],
            "junho": ["junho", "jun"],
            "julho": ["julho", "jul"],
            "agosto": ["agosto", "ago"],
            "setembro": ["setembro", "set"],
            "outubro": ["outubro", "out"],
            "novembro": ["novembro", "nov"],
            "dezembro": ["dezembro", "dez"]
        }
        
        for month, keywords in period_corrections.items():
            if any(keyword in feedback_lower for keyword in keywords):
                # Substituir período na pergunta original
                import re
                # Padrão para capturar meses na pergunta original
                month_pattern = r'\b(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)\b'
                if re.search(month_pattern, original_question.lower()):
                    corrected = re.sub(month_pattern, month, original_question, flags=re.IGNORECASE)
                    return corrected
                else:
                    # Adicionar o mês se não existir
                    corrected = f"{original_question} em {month}"
                    return corrected
        
        # Se não conseguir detectar correção específica, retornar pergunta original
        # com uma nota sobre o feedback
        return f"{original_question} (considerando feedback: {feedback})"

    def _process_with_datahero_pipeline(self, message: str, session: ChatSession, is_followup: bool = False) -> str:
        """Processa a mensagem usando o pipeline real do DataHero4."""
        try:
            # Configurar logging baseado no nível de verbosidade
            self._setup_pipeline_logging(session)

            # Mostrar progresso amigável (exceto em modo quiet)
            if session.verbose_level > 0:
                console.print("🔍 [bold blue]Analisando pergunta...[/bold blue]")

            # Importar o pipeline otimizado
            from ..graphs.optimized_workflow import create_optimized_workflow
            from ..graphs.state import DataHeroState
            import uuid

            # Criar workflow
            if session.verbose_level > 0:
                console.print("⚙️ [bold]Inicializando pipeline...[/bold]")
            workflow = create_optimized_workflow()

            # Preparar estado inicial - usar thread_id consistente para preservar contexto
            thread_id = f"cli_{session.thread_id}"

            # Definir caminhos de configuração
            base_config_path = f"src/config/setores/{session.sector}/{session.client_id}"

            initial_state = DataHeroState(
                question=message,
                client_id=session.client_id,
                sector=session.sector,
                channel="cli",
                thread_id=thread_id,
                session_id=session.thread_id,
                is_conversational=is_followup,
                enable_learning=True,
                enable_feedback=True,
                enable_cache=True,
                enable_patterns=True,
                retry_count=0,
                max_retries=3,
                coordinator_iterations=0,
                _source="enhanced_cli",
                # Adicionar caminhos de configuração necessários
                schema_relevance_path=f"{base_config_path}/{session.client_id}_schema_relevance.json",
                sector_kpi_path=f"src/config/setores/{session.sector}/kpis-exchange-json.json",
                llm_config_path=f"{base_config_path}/llm.yaml",
                prompt_path=f"{base_config_path}/prompts/query_generator.txt",
                prompt_path_business_analyst=f"{base_config_path}/prompts/business_analyst.txt",
                prompt_path_insight_generator=f"{base_config_path}/prompts/insight_generator.txt",
                prompt_path_question_suggester=f"{base_config_path}/prompts/question_suggester.txt",
                categorical_values_path=f"{base_config_path}/colunas_categoricas.json",
                schema={},
                categorical_values={},
                messages=[],
                kpi_context={},
                attempts=0,
                user_role=None
            )

            # Configuração para o checkpointer
            config = {"configurable": {"thread_id": thread_id}}

            # Executar pipeline com indicadores de progresso
            if session.verbose_level > 0:
                console.print("🚀 [bold green]Executando análise...[/bold green]")

            final_state = workflow.invoke(initial_state, config=config)

            if session.verbose_level > 0:
                console.print("✅ [bold green]Análise concluída![/bold green]")

            # Extrair resposta do estado final
            return self._format_pipeline_response(final_state, session)

        except Exception as e:
            console.print(f"[red]❌ Erro no pipeline: {e}[/red]")
            return f"❌ **Erro no Processamento**\n\nOcorreu um erro ao processar sua pergunta: {str(e)}\n\nTente novamente ou reformule sua pergunta."

    def _setup_pipeline_logging(self, session: ChatSession):
        """Configura o logging do pipeline baseado no nível de verbosidade."""
        import logging
        import sys
        from rich.logging import RichHandler

        # Remover handlers existentes para evitar duplicação
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        # Configurar nível baseado na verbosidade
        if session.verbose_level == 0:  # Quiet mode
            log_level = logging.ERROR
        elif session.verbose_level == 1:  # Normal
            log_level = logging.WARNING
        elif session.verbose_level == 2:  # Verbose
            log_level = logging.INFO
        else:  # Debug (3+)
            log_level = logging.DEBUG

        # Criar handler customizado com Rich
        class PipelineLogHandler(RichHandler):
            def __init__(self, session):
                super().__init__(
                    console=console,
                    show_time=False,
                    show_path=False,
                    markup=True,
                    rich_tracebacks=True
                )
                self.session = session

            def format(self, record):
                # Mapear níveis para cores e ícones
                level_styles = {
                    'DEBUG': ('[dim]🔍', '[/dim]'),
                    'INFO': ('[blue]ℹ️', '[/blue]'),
                    'WARNING': ('[yellow]⚠️', '[/yellow]'),
                    'ERROR': ('[red]❌', '[/red]'),
                    'CRITICAL': ('[bold red]🚨', '[/bold red]')
                }

                icon, end_style = level_styles.get(record.levelname, ('📝', ''))

                # Formatação especial para diferentes tipos de mensagem
                msg = record.getMessage()

                # Mensagens de API calls
                if 'Chamando' in msg and 'AI' in msg:
                    return f"[cyan]🤖 {msg}[/cyan]"
                elif 'Resposta recebida' in msg:
                    return f"[green]✅ {msg}[/green]"

                # Mensagens de SQL
                elif 'VALIDANDO QUERY SQL' in msg:
                    return f"[magenta]🔍 Validando SQL...[/magenta]"
                elif 'Executando query' in msg:
                    return f"[blue]🗄️ Executando consulta...[/blue]"
                elif 'Resultado da query' in msg:
                    return f"[green]📊 Dados obtidos com sucesso[/green]"

                # Mensagens de agentes
                elif 'Business Analysis Agent' in msg:
                    return f"[purple]🧠 Analisando resultados...[/purple]"
                elif 'SQL Executor Agent' in msg:
                    return f"[blue]⚡ Executando SQL...[/blue]"

                # Mensagens de validação
                elif 'Validação inteligente' in msg and 'Válido: True' in msg:
                    return f"[green]✅ SQL validado com sucesso[/green]"
                elif 'AVISO' in msg or 'WARNING' in msg:
                    # Simplificar avisos técnicos
                    if 'não encontrada no schema' in msg:
                        return f"[dim yellow]⚠️ Validação de schema (não crítico)[/dim yellow]"
                    return f"[yellow]⚠️ {msg}[/yellow]"

                # Mensagens de debug (apenas em modo debug)
                elif record.levelname == 'DEBUG':
                    if self.session.verbose_level >= 3:
                        return f"[dim]{icon} {msg}{end_style}[/dim]"
                    else:
                        return None  # Suprimir debug em modos menos verbosos

                # Outras mensagens INFO
                elif record.levelname == 'INFO':
                    # Filtrar mensagens muito técnicas em modo normal
                    if self.session.verbose_level < 2:
                        technical_keywords = ['Cache construído', 'carregado de', 'ENTERING', 'EXITING']
                        if any(keyword in msg for keyword in technical_keywords):
                            return None

                    return f"{icon} [dim]{msg}[/dim]{end_style}"

                # Mensagens de erro sempre mostrar
                else:
                    return f"{icon} {msg}{end_style}"

        # Configurar handler
        handler = PipelineLogHandler(session)
        handler.setLevel(log_level)

        # Configurar logger root
        logging.basicConfig(
            level=log_level,
            handlers=[handler],
            force=True
        )

        # Configurar loggers específicos
        loggers_to_configure = [
            'src.agents.business_analyst',
            'src.agents.sql_executor',
            'src.tools.nivel2.progressive_fallback',
            'src.tools.nivel2.sql_validator',
            'src.tools.nivel2.column_matcher'
        ]

        for logger_name in loggers_to_configure:
            logger = logging.getLogger(logger_name)
            logger.setLevel(log_level)
            logger.handlers = [handler]
            logger.propagate = False

    def _format_sql_for_display(self, sql_query: str) -> str:
        """Formata SQL para exibição elegante na CLI."""
        try:
            # Tentar formatar com sqlparse se disponível
            try:
                import sqlparse
                formatted = sqlparse.format(
                    sql_query,
                    reindent=True,
                    keyword_case='upper',
                    strip_comments=True
                )
                return f"```sql\n{formatted}\n```"
            except ImportError:
                # Fallback para formatação manual simples
                lines = sql_query.strip().split('\n')
                formatted_lines = []
                for line in lines:
                    line = line.strip()
                    if line:
                        formatted_lines.append(f"  {line}")

                return f"```sql\n" + "\n".join(formatted_lines) + "\n```"
        except Exception:
            # Em caso de erro, retornar SQL original
            return f"```sql\n{sql_query}\n```"

    def _format_pipeline_response(self, state: dict, session: Optional[ChatSession] = None) -> str:
        """Formata a resposta do pipeline para exibição amigável na CLI."""
        try:
            # Extrair componentes da resposta
            business_analysis = state.get("business_analysis", "")
            sql_query = state.get("sql_query", "")
            query_result = state.get("query_result", [])

            # Construir resposta formatada
            response_parts = []

            # 1. Análise de Negócio (formatada de forma amigável)
            if business_analysis:
                formatted_analysis = self._format_business_analysis(business_analysis)
                response_parts.append(formatted_analysis)

            # 2. Resultados dos Dados (formatados de forma amigável)
            if query_result:
                formatted_results = self._format_query_results(query_result)
                response_parts.append(formatted_results)

            # 3. SQL Query (opcional, apenas se debug estiver ativo ou verbose muito alto)
            if sql_query and session and (session.debug_mode or session.verbose_level >= 3):
                # Formatação mais elegante do SQL
                formatted_sql = self._format_sql_for_display(sql_query)
                response_parts.append(f"\n🔍 **SQL Executado:**\n{formatted_sql}")

            if response_parts:
                return "\n\n".join(response_parts)
            else:
                return "✅ **Consulta Processada**\n\nSua pergunta foi processada com sucesso, mas não foram encontrados resultados específicos para exibir."

        except Exception as e:
            return f"⚠️ **Resposta Parcial**\n\nA consulta foi processada, mas houve um problema na formatação da resposta: {str(e)}"

    def _format_business_analysis(self, analysis) -> str:
        """Formata a análise de negócio de forma amigável."""
        try:
            if isinstance(analysis, dict):
                # Se for um dicionário, extrair a resposta direta
                direct_answer = analysis.get("direct_answer", "")
                if direct_answer:
                    return f"💡 **Resposta:**\n\n{direct_answer}"
                else:
                    # Fallback para outros campos
                    return f"📊 **Análise:**\n\n{str(analysis)}"
            elif isinstance(analysis, str):
                # Se for string, usar diretamente
                return f"💡 **Resposta:**\n\n{analysis}"
            else:
                return f"📊 **Análise:**\n\n{str(analysis)}"
        except Exception as e:
            return f"📊 **Análise:**\n\n{str(analysis)}"

    def _format_query_results(self, results) -> str:
        """Formata os resultados da query de forma amigável."""
        try:
            if not results or len(results) == 0:
                return "📈 **Resultados:** Nenhum dado encontrado"

            result_count = len(results)

            # Para um único resultado com valor numérico, formatação especial
            if result_count == 1 and isinstance(results[0], dict):
                first_result = results[0]

                # Detectar valores monetários ou numéricos importantes
                for key, value in first_result.items():
                    if any(term in key.lower() for term in ['volume', 'valor', 'total', 'soma', 'quantidade']):
                        if isinstance(value, (int, float)) or (hasattr(value, '__float__')):
                            try:
                                numeric_value = float(value)
                                if numeric_value >= 1000000:  # Valores em milhões
                                    formatted_value = f"R$ {numeric_value:,.2f}".replace(',', '.')
                                    return f"📊 **Resultado:**\n\n{formatted_value}"
                                elif numeric_value >= 1000:  # Valores em milhares
                                    formatted_value = f"R$ {numeric_value:,.2f}".replace(',', '.')
                                    return f"📊 **Resultado:**\n\n{formatted_value}"
                                else:
                                    return f"📊 **Resultado:**\n\n{value}"
                            except:
                                pass

            # Formatação padrão para múltiplos resultados
            formatted_parts = [f"📈 **Resultados:** {result_count} registro(s) encontrado(s)\n"]

            if result_count <= 5:
                formatted_parts.append("**Dados:**")
                for i, row in enumerate(results[:5], 1):
                    formatted_parts.append(f"  {i}. {self._format_single_result(row)}")
            else:
                formatted_parts.append("**Primeiros 3 resultados:**")
                for i, row in enumerate(results[:3], 1):
                    formatted_parts.append(f"  {i}. {self._format_single_result(row)}")
                formatted_parts.append(f"  ... e mais {result_count - 3} registros")

            return "\n".join(formatted_parts)

        except Exception as e:
            return f"📈 **Resultados:** {len(results)} registro(s) - {str(e)}"

    def _format_single_result(self, result) -> str:
        """Formata um único resultado de forma legível."""
        try:
            if isinstance(result, dict):
                formatted_items = []
                for key, value in result.items():
                    # Formatação especial para valores monetários
                    if isinstance(value, (int, float)) or (hasattr(value, '__float__')):
                        try:
                            numeric_value = float(value)
                            if any(term in key.lower() for term in ['valor', 'volume', 'total', 'soma']):
                                formatted_value = f"R$ {numeric_value:,.2f}".replace(',', '.')
                                formatted_items.append(f"{key}: {formatted_value}")
                            else:
                                formatted_items.append(f"{key}: {value}")
                        except:
                            formatted_items.append(f"{key}: {value}")
                    else:
                        formatted_items.append(f"{key}: {value}")
                return " | ".join(formatted_items)
            else:
                return str(result)
        except Exception as e:
            return str(result)

    def display_response(self, content: str, session: ChatSession):
        """Exibe a resposta formatada."""
        api_indicator = "🧠 LLM" if session.use_real_apis else "🔧 Simulação"

        response_panel = Panel(
            content,
            title=f"🤖 DataHero4 Assistant ({api_indicator})",
            border_style="green",
            padding=(1, 2)
        )

        console.print(response_panel)

    def display_performance_metrics(self, processing_time: float, classification: MessageClassification, session: ChatSession):
        """Exibe métricas de performance de forma amigável."""
        # Não mostrar métricas em modo quiet
        if session.verbose_level == 0:
            return

        method = "🧠 LLM Real" if "LLM:" in classification.reasoning else "🔧 Heurística"

        # Formatação amigável do tempo
        if processing_time < 1:
            time_display = f"{processing_time*1000:.0f}ms"
        elif processing_time < 60:
            time_display = f"{processing_time:.1f}s"
        else:
            minutes = int(processing_time // 60)
            seconds = processing_time % 60
            time_display = f"{minutes}m {seconds:.1f}s"

        # Formatação amigável da confiança
        confidence_percent = f"{classification.confidence*100:.0f}%"
        confidence_emoji = "🎯" if classification.confidence >= 0.9 else "⚡" if classification.confidence >= 0.7 else "⚠️"

        # Status das APIs
        api_status = "✅ Ativas" if session.use_real_apis else "🔄 Simulação"

        # Versão compacta para modo normal (verbose_level = 1)
        if session.verbose_level == 1:
            metrics_content = f"""[bold]⏱️ Tempo:[/bold] {time_display} | [bold]{confidence_emoji} Confiança:[/bold] {confidence_percent} | [bold]🔌 APIs:[/bold] {api_status}"""

            panel = Panel(
                metrics_content,
                title="📊 Performance",
                border_style="dim",
                padding=(0, 1)
            )
        else:
            # Versão completa para modo verbose alto
            metrics_content = f"""[bold]⏱️ Tempo:[/bold] {time_display}
[bold]🔬 Método:[/bold] {method}
[bold]{confidence_emoji} Confiança:[/bold] {confidence_percent}
[bold]💬 Mensagens:[/bold] {session.message_count + 1}
[bold]🔌 APIs:[/bold] {api_status}"""

            panel = Panel(
                metrics_content,
                title="📊 Performance Detalhada",
                border_style="bright_blue",
                padding=(0, 1)
            )

        console.print(panel)

    def display_session_summary(self, session: ChatSession):
        """Exibe resumo da sessão ao final."""
        duration = datetime.now() - session.created_at

        # Contar métodos usados
        llm_count = sum(1 for entry in session.conversation_history if entry.get("used_llm", False))
        heuristic_count = len(session.conversation_history) - llm_count

        summary_content = f"""[bold]📊 Resumo da Sessão[/bold]

[bold]⏱️ Duração:[/bold] {duration}
[bold]💬 Mensagens:[/bold] {session.message_count}
[bold]🧵 Thread ID:[/bold] {session.thread_id}
[bold]🔌 APIs Usadas:[/bold] {'Sim' if session.use_real_apis else 'Não'}

[bold]📈 Métodos de Classificação:[/bold]
  🧠 LLM Real: {llm_count}
  🔧 Heurística: {heuristic_count}"""

        panel = Panel(
            summary_content,
            title="🏁 Sessão Finalizada",
            border_style="magenta",
            padding=(1, 2)
        )

        console.print(panel)


# ============================================================================
# PONTO DE ENTRADA PRINCIPAL
# ============================================================================

app = typer.Typer(
    name="enhanced-chat",
    help="CLI Interativa Aprimorada para DataHero4",
    add_completion=False
)


@app.command()
def main(
    client_id: str = typer.Option("L2M", "--client-id", "-c", help="ID do cliente"),
    sector: str = typer.Option("cambio", "--sector", "-s", help="Setor do cliente"),
    user_id: str = typer.Option("demo-user", "--user-id", "-u", help="ID do usuário"),
    debug: bool = typer.Option(False, "--debug", "-d", help="Ativar modo debug"),
    verbose: int = typer.Option(1, "--verbose", "-v", help="Nível de verbosidade (1-3)", min=1, max=3),
    use_real_apis: bool = typer.Option(False, "--use-real-apis", help="Usar APIs reais de LLM")
):
    """
    Inicia a CLI interativa aprimorada do DataHero4.

    Esta versão integra com APIs reais de LLM quando disponíveis,
    oferecendo classificação mais precisa de entrada conversacional.

    Exemplos:
        poetry run python -m src.cli.enhanced_chat_cli
        poetry run python -m src.cli.enhanced_chat_cli --debug --use-real-apis
        poetry run python -m src.cli.enhanced_chat_cli --verbose 3
    """

    async def run_cli():
        cli = EnhancedChatCLI(use_real_apis=use_real_apis)

        try:
            # Criar sessão inicial
            session = cli.create_new_session(
                user_id=user_id,
                client_id=client_id,
                sector=sector,
                debug_mode=debug
            )
            session.verbose_level = verbose

            # Executar loop interativo
            await cli.run_interactive_loop(session)

        except KeyboardInterrupt:
            console.print("\n[bold red]🛑 CLI interrompida pelo usuário[/bold red]")
        except Exception as e:
            console.print(f"[red]❌ Erro fatal: {e}[/red]")
            raise typer.Exit(1)

    # Executar CLI assíncrona
    asyncio.run(run_cli())


if __name__ == "__main__":
    app()
