"""
Critical KPIs Configuration for MVP
===================================

Define os 6 KPIs críticos para operadoras de câmbio baseado em 
melhores práticas do setor.
"""

from typing import List, Dict, Any
from enum import Enum

class KpiPriority(str, Enum):
    CRITICAL = "critical"  # 6 KPIs do MVP
    HIGH = "high"         # Próxima fase
    MEDIUM = "medium"     # Futura expansão
    LOW = "low"          # Nice to have

class CriticalKpisConfig:
    """
    Define os 6 KPIs críticos baseados em pesquisa de mercado:
    
    1. Total Volume - Volume total negociado
    2. Average Spread - Margem de spread cambial
    3. Conversion Rate - Taxa de conversão de cotações
    4. Average Ticket - Ticket médio
    5. Retention Rate - Taxa de retenção de clientes
    6. Operations per Analyst - Operações por analista
    """
    
    # IDs dos KPIs críticos (devem corresponder aos IDs no banco)
    CRITICAL_KPI_IDS = [
        "total_volume",              # Volume total negociado
        "average_spread",            # Spread médio FX
        "conversion_rate",           # Taxa de conversão
        "average_ticket",            # Ticket médio
        "retention_rate",            # Taxa de retenção de clientes
        "operations_per_analyst"     # Operações por analista
    ]
    
    @classmethod
    def get_critical_kpi_ids(cls) -> List[str]:
        """Retorna lista de IDs dos KPIs críticos."""
        return cls.CRITICAL_KPI_IDS
    
    @classmethod
    def is_critical_kpi(cls, kpi_id: str) -> bool:
        """Verifica se um KPI é crítico."""
        return kpi_id in cls.CRITICAL_KPI_IDS
    
    @classmethod
    def get_kpi_metadata(cls) -> Dict[str, Dict[str, Any]]:
        """Retorna metadados dos KPIs críticos para referência."""
        return {
            "total_volume": {
                "name": "Volume Total Negociado",
                "description": "Mede o tamanho da operação e é o indicador primário de crescimento",
                "category": "operational",
                "format_type": "currency",
                "icon": "TrendingUp",
                "priority": 1,
                "is_priority": True
            },
            "average_spread": {
                "name": "Spread Médio",
                "description": "Principal indicador de rentabilidade - agora em percentual",
                "category": "financial",
                "format_type": "percentage",
                "icon": "DollarSign",
                "priority": 2,
                "is_priority": True
            },
            "conversion_rate": {
                "name": "Taxa de Conversão",
                "description": "Eficácia comercial",
                "category": "client",
                "format_type": "percentage",
                "icon": "Target",
                "priority": 3,
                "is_priority": True
            },
            "average_ticket": {
                "name": "Ticket Médio",
                "description": "Indica perfil de clientes e operações",
                "category": "operational",
                "format_type": "currency",
                "icon": "CreditCard",
                "priority": 4,
                "is_priority": True
            },
            "retention_rate": {
                "name": "Taxa de Retenção",
                "description": "Fidelização de clientes",
                "category": "client",
                "format_type": "percentage",
                "icon": "Users",
                "priority": 5,
                "is_priority": True
            },
            "operations_per_analyst": {
                "name": "Operações por Analista/Dia",
                "description": "Produtividade da equipe",
                "category": "team",
                "format_type": "number",
                "icon": "Activity",
                "priority": 6,
                "is_priority": True
            }
        }
