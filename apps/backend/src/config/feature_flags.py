"""
Feature Flags Configuration for DataHero4
==========================================

Centralized feature flag management for enabling/disabling features
based on environment variables.
"""

import os
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class FeatureFlags:
    """
    Centralized feature flag management.
    
    All flags are controlled via environment variables and can be
    toggled without code changes.
    """
    
    def __init__(self):
        """Initialize feature flags from environment variables."""
        self.flags = {
            # Core system flags
            'mvp_mode': os.getenv('MVP_MODE', 'true').lower() == 'true',
            'debug_mode': os.getenv('DEBUG', 'false').lower() == 'true',
            'dev_mode': os.getenv('DEV_MODE', 'false').lower() == 'true',
            
            # Dashboard and KPI flags
            'dashboard_snapshot': os.getenv('DASHBOARD_SNAPSHOT', 'true').lower() == 'true',
            'dashboard_real_data': os.getenv('DASHBOARD_REAL_DATA', 'true').lower() == 'true',
            
            # LangGraph and AI flags
            'langgraph_agents': os.getenv('LANGGRAPH_ENABLED', 'false').lower() == 'true',
            'use_langgraph_pipeline': os.getenv('USE_LANGGRAPH_PIPELINE', 'false').lower() == 'true',
            
            # Learning and feedback flags
            'enable_learning_infrastructure': os.getenv('ENABLE_LEARNING_INFRASTRUCTURE', 'false').lower() == 'true',
            'enable_feedback_system': os.getenv('ENABLE_FEEDBACK_SYSTEM', 'false').lower() == 'true',
            
            # Cache and optimization flags
            'enable_cache_system': os.getenv('ENABLE_CACHE_SYSTEM', 'false').lower() == 'true',
            'enable_pattern_matching': os.getenv('ENABLE_PATTERN_MATCHING', 'false').lower() == 'true',
            
            # Advanced features
            'advanced_analytics': os.getenv('ADVANCED_ANALYTICS', 'false').lower() == 'true',
            'domain_extraction': os.getenv('ENABLE_DOMAIN_EXTRACTION', 'false').lower() == 'true',
        }
        
        # Log enabled flags for debugging
        enabled_flags = [flag for flag, enabled in self.flags.items() if enabled]
        if enabled_flags:
            logger.info(f"🚩 Enabled feature flags: {', '.join(enabled_flags)}")
    
    def is_enabled(self, flag_name: str) -> bool:
        """
        Check if a feature flag is enabled.
        
        Args:
            flag_name: Name of the feature flag
            
        Returns:
            True if flag is enabled, False otherwise
        """
        return self.flags.get(flag_name, False)
    
    def get_all_flags(self) -> Dict[str, bool]:
        """Return all feature flags and their current state."""
        return self.flags.copy()
    
    def get_enabled_flags(self) -> Dict[str, bool]:
        """Return only enabled feature flags."""
        return {flag: enabled for flag, enabled in self.flags.items() if enabled}
    
    def set_flag(self, flag_name: str, enabled: bool) -> None:
        """
        Set a feature flag programmatically (for testing).
        
        Args:
            flag_name: Name of the feature flag
            enabled: Whether to enable or disable the flag
        """
        self.flags[flag_name] = enabled
        logger.debug(f"Feature flag '{flag_name}' set to {enabled}")

# Global instance
feature_flags = FeatureFlags()

# Convenience functions for common checks
def is_mvp_mode() -> bool:
    """Check if system is running in MVP mode."""
    return feature_flags.is_enabled('mvp_mode')

def is_dev_mode() -> bool:
    """Check if system is running in development mode."""
    return feature_flags.is_enabled('dev_mode')

def is_dashboard_snapshot_enabled() -> bool:
    """Check if dashboard snapshot feature is enabled."""
    return feature_flags.is_enabled('dashboard_snapshot')

def is_real_data_enabled() -> bool:
    """Check if real data should be used instead of mocks."""
    return feature_flags.is_enabled('dashboard_real_data')
