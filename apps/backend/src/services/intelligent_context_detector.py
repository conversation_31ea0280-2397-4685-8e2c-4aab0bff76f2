"""
Intelligent Context Detector for DataHero4
========================================

Sistema inteligente de detecção de contexto que combina patterns hardcoded
com análise LLM quando necessário. Totalmente síncrono para compatibilidade
com Railway deployment.

Features:
- Smart routing baseado em complexidade
- LLM analysis para casos complexos
- Caching agressivo para performance
- Threading paralelo para otimização
- 100% sync para Railway compatibility
"""

import logging
import json
import hashlib
import time
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import re

from ..tools.llm_provider import LLMProvider
from .conversational_entity_extractor import ConversationalEntityExtractor, ExtractedEntity
from ..utils.context_debug_logger import context_debug_logger
from ..config.intelligent_context_config import get_intelligent_context_config

logger = logging.getLogger(__name__)


class ContextComplexity(str, Enum):
    """Níveis de complexidade para routing inteligente."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"


class DetectionStrategy(str, Enum):
    """Estratégias de detecção disponíveis."""
    HARDCODED = "hardcoded"
    HYBRID = "hybrid"
    FULL_LLM = "full_llm"


@dataclass
class ContextAnalysisResult:
    """Resultado da análise inteligente de contexto."""
    entities: Dict[str, List[ExtractedEntity]]
    confidence: float
    strategy_used: DetectionStrategy
    processing_time: float
    llm_calls_made: int
    cache_hit: bool
    complexity_level: ContextComplexity
    reasoning: str
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Converte resultado para dicionário."""
        return {
            "entities": {
                k: [e.to_dict() for e in v] 
                for k, v in self.entities.items()
            },
            "confidence": self.confidence,
            "strategy_used": self.strategy_used,
            "processing_time": self.processing_time,
            "llm_calls_made": self.llm_calls_made,
            "cache_hit": self.cache_hit,
            "complexity_level": self.complexity_level,
            "reasoning": self.reasoning,
            "metadata": self.metadata
        }


class IntelligentContextDetector:
    """
    Detector inteligente de contexto que usa routing adapativo.
    
    Combina:
    - Patterns hardcoded para casos simples (50-100ms)
    - Análise híbrida para casos moderados (500-1500ms)
    - Análise LLM completa para casos complexos (2000-3000ms)
    """
    
    def __init__(self, sector="cambio", client_id="L2M"):
        """Inicializa detector inteligente."""
        self.sector = sector
        self.client_id = client_id
        
        # Load configuration
        self.config = get_intelligent_context_config()
        
        # Components
        self.hardcoded_extractor = ConversationalEntityExtractor()
        
        # Only initialize LLM if enabled
        if self.config.enabled:
            self.llm_provider = LLMProvider(sector, client_id, "context_detector")
        else:
            self.llm_provider = None
        
        # Cache para resultados LLM
        self.analysis_cache = {}
        self.cache_max_size = self.config.cache_max_size
        self.cache_ttl = self.config.cache_ttl_seconds
        
        # Patterns para complexity detection
        self.complexity_patterns = self._load_complexity_patterns()
        
        # Threading pool para operations paralelas
        self.max_workers = self.config.max_workers
        
        logger.info(f"🧠 IntelligentContextDetector initialized "
                   f"(enabled: {self.config.enabled}, sync: {self.config.sync_only})")
    
    def _load_complexity_patterns(self) -> Dict[str, List[str]]:
        """Carrega patterns para detectar complexidade da query."""
        return {
            "simple_indicators": [
                r"^(qual|quanto|quantos?)\s+(o|a|os|as)?\s*\w+\?*$",
                r"^(mostre?|liste?)\s+\w+",
                r"^(total|soma|média)\s+(de|dos?|das?)\s+\w+",
                r"^\w+\s+(hoje|ontem|agora)$"
            ],
            "complex_indicators": [
                r"(comparar?|versus|vs\.?|diferença|variação)",
                r"(porque|por que|motivo|razão|causa)",
                r"(tendência|padrão|correlação|relação)",
                r"(análise|insights?|conclusão|interpretação)",
                r"(projeção|previsão|estimativa|forecast)",
                r"(isso|aquilo|anterior|último|passado).*(incorreto|errado|não)",
                r"(deveria|poderia|seria melhor|prefiro)"
            ],
            "temporal_complex": [
                r"(período|durante|entre|desde|até).*\d{4}",
                r"(comparado|em relação|versus).*\d{4}",
                r"(ano|mês|trimestre).*(\d{4}|\w+).*(\d{4}|\w+)"
            ],
            "reference_indicators": [
                r"(isso|aquilo|anterior|último|essas?|aquelas?)",
                r"(mesmos?|iguais?|similares?|parecidos?)",
                r"(como|igual|tal como|assim como)"
            ]
        }
    
    def detect_context(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> ContextAnalysisResult:
        """
        Detecta contexto usando routing inteligente.
        
        Args:
            query: Query do usuário
            conversation_history: Histórico da conversa
            
        Returns:
            ContextAnalysisResult: Resultado da análise
        """
        start_time = time.time()
        logger.info(f"🧠 Analyzing context for query: {query[:50]}...")
        
        try:
            # 0. Check if system is enabled
            if not self.config.enabled:
                logger.info("🔄 Intelligent context detection disabled, using hardcoded fallback")
                return self._fallback_detection(query, conversation_history, start_time)
            
            # 1. Cache lookup
            cache_key = self._generate_cache_key(query, conversation_history)
            if self.config.cache_enabled and (cached_result := self._get_from_cache(cache_key)):
                cached_result.cache_hit = True
                cached_result.processing_time = time.time() - start_time
                logger.info(f"✅ Cache hit for context analysis")
                return cached_result
            
            # 2. Complexity analysis
            complexity = self._analyze_complexity(query, conversation_history)
            
            # 3. Strategy selection
            strategy = self._select_strategy(complexity, query)
            
            # 4. Context detection baseado na strategy
            result = self._execute_detection_strategy(
                strategy, complexity, query, conversation_history, start_time
            )
            
            # 5. Cache result
            self._cache_result(cache_key, result)
            
            logger.info(
                f"✅ Context analysis complete: {strategy} "
                f"(complexity: {complexity}, time: {result.processing_time:.2f}s)"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error in intelligent context detection: {e}")
            # Fallback para hardcoded extraction
            return self._fallback_detection(query, conversation_history, start_time)
    
    def _analyze_complexity(
        self, 
        query: str, 
        conversation_history: Optional[List[Dict[str, Any]]]
    ) -> ContextComplexity:
        """Analisa complexidade da query para routing."""
        query_lower = query.lower()
        
        # Check simple patterns
        for pattern in self.complexity_patterns["simple_indicators"]:
            if re.search(pattern, query_lower):
                return ContextComplexity.SIMPLE
        
        # Count complexity indicators
        complex_score = 0
        
        # Complex language patterns
        for pattern in self.complexity_patterns["complex_indicators"]:
            if re.search(pattern, query_lower):
                complex_score += 2
        
        # Temporal complexity
        for pattern in self.complexity_patterns["temporal_complex"]:
            if re.search(pattern, query_lower):
                complex_score += 1
        
        # Reference complexity
        for pattern in self.complexity_patterns["reference_indicators"]:
            if re.search(pattern, query_lower):
                complex_score += 1
        
        # Conversation context complexity
        if conversation_history and len(conversation_history) > 2:
            complex_score += 1
        
        # Multi-sentence complexity
        if len(query.split('.')) > 1 or len(query.split(',')) > 2:
            complex_score += 1
        
        # Determine complexity level
        if complex_score >= 4:
            return ContextComplexity.COMPLEX
        elif complex_score >= 2:
            return ContextComplexity.MODERATE
        else:
            return ContextComplexity.SIMPLE
    
    def _select_strategy(self, complexity: ContextComplexity, query: str) -> DetectionStrategy:
        """Seleciona strategy baseada na complexidade."""
        if complexity == ContextComplexity.SIMPLE:
            return DetectionStrategy.HARDCODED
        elif complexity == ContextComplexity.MODERATE:
            return DetectionStrategy.HYBRID
        else:
            return DetectionStrategy.FULL_LLM
    
    def _execute_detection_strategy(
        self,
        strategy: DetectionStrategy,
        complexity: ContextComplexity,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]],
        start_time: float
    ) -> ContextAnalysisResult:
        """Executa strategy de detecção selecionada."""
        
        if strategy == DetectionStrategy.HARDCODED:
            return self._hardcoded_detection(query, conversation_history, complexity, start_time)
        
        elif strategy == DetectionStrategy.HYBRID:
            return self._hybrid_detection(query, conversation_history, complexity, start_time)
        
        else:  # FULL_LLM
            return self._full_llm_detection(query, conversation_history, complexity, start_time)
    
    def _hardcoded_detection(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]],
        complexity: ContextComplexity,
        start_time: float
    ) -> ContextAnalysisResult:
        """Detecção usando apenas patterns hardcoded."""
        entities = self.hardcoded_extractor.extract_entities(query, conversation_history)
        
        # Calculate confidence based on entities found
        confidence = self._calculate_hardcoded_confidence(entities)
        
        return ContextAnalysisResult(
            entities=entities,
            confidence=confidence,
            strategy_used=DetectionStrategy.HARDCODED,
            processing_time=time.time() - start_time,
            llm_calls_made=0,
            cache_hit=False,
            complexity_level=complexity,
            reasoning="Simple query detected, using hardcoded patterns",
            metadata={
                "patterns_matched": len([e for entities_list in entities.values() for e in entities_list]),
                "extraction_method": "regex_patterns"
            }
        )
    
    def _hybrid_detection(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]],
        complexity: ContextComplexity,
        start_time: float
    ) -> ContextAnalysisResult:
        """Detecção híbrida: hardcoded + LLM validation."""
        
        # Execute hardcoded and LLM analysis in parallel
        with ThreadPoolExecutor(max_workers=2) as executor:
            # Start hardcoded extraction
            hardcoded_future = executor.submit(
                self.hardcoded_extractor.extract_entities, 
                query, 
                conversation_history
            )
            
            # Start LLM validation
            llm_future = executor.submit(
                self._llm_entity_validation,
                query,
                conversation_history
            )
            
            # Collect results
            hardcoded_entities = hardcoded_future.result()
            llm_validation = llm_future.result()
        
        # Merge results intelligently
        merged_entities = self._merge_hardcoded_and_llm(hardcoded_entities, llm_validation)
        
        # Calculate hybrid confidence
        confidence = self._calculate_hybrid_confidence(hardcoded_entities, llm_validation)
        
        return ContextAnalysisResult(
            entities=merged_entities,
            confidence=confidence,
            strategy_used=DetectionStrategy.HYBRID,
            processing_time=time.time() - start_time,
            llm_calls_made=1,
            cache_hit=False,
            complexity_level=complexity,
            reasoning="Moderate complexity, using hybrid approach with LLM validation",
            metadata={
                "hardcoded_entities": len([e for entities_list in hardcoded_entities.values() for e in entities_list]),
                "llm_enhanced": True,
                "validation_score": llm_validation.get("confidence", 0.0)
            }
        )
    
    def _full_llm_detection(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]],
        complexity: ContextComplexity,
        start_time: float
    ) -> ContextAnalysisResult:
        """Detecção completa usando LLM."""
        
        # Execute multiple LLM analyses in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {
                "entity_extraction": executor.submit(
                    self._llm_entity_extraction, query, conversation_history
                ),
                "temporal_analysis": executor.submit(
                    self._llm_temporal_analysis, query, conversation_history
                ),
                "reference_resolution": executor.submit(
                    self._llm_reference_resolution, query, conversation_history
                )
            }
            
            # Collect results
            results = {}
            for key, future in futures.items():
                try:
                    results[key] = future.result()
                except Exception as e:
                    logger.error(f"❌ Error in {key}: {e}")
                    results[key] = {"entities": {}, "confidence": 0.0}
        
        # Synthesize LLM results
        entities = self._synthesize_llm_results(results)
        confidence = self._calculate_llm_confidence(results)
        
        return ContextAnalysisResult(
            entities=entities,
            confidence=confidence,
            strategy_used=DetectionStrategy.FULL_LLM,
            processing_time=time.time() - start_time,
            llm_calls_made=3,
            cache_hit=False,
            complexity_level=complexity,
            reasoning="Complex query requiring full LLM analysis",
            metadata={
                "llm_analyses": list(results.keys()),
                "synthesis_method": "weighted_merge",
                "individual_confidences": {k: v.get("confidence", 0.0) for k, v in results.items()}
            }
        )
    
    def _llm_entity_validation(self, query: str, conversation_history: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """LLM validation of hardcoded entities."""
        if not self.llm_provider:
            logger.warning("🔄 LLM provider not available for entity validation")
            return {"missing_entities": [], "confidence": 0.5, "temporal_intent": "none"}
            
        history_text = self._format_conversation_history(conversation_history)
        
        prompt = f"""Analise esta query e valide as entidades detectadas:

QUERY: "{query}"
HISTÓRICO: {history_text}

Tarefa: Determine se a query contém entidades importantes que podem ter sido perdidas por patterns simples.

Responda APENAS com JSON:
{{
    "missing_entities": ["lista de entidades importantes não detectadas"],
    "confidence": float,
    "temporal_intent": "explicit|implicit|none",
    "business_context_needed": boolean,
    "reasoning": "explicação breve"
}}"""
        
        try:
            response = self.llm_provider.invoke(prompt)
            return json.loads(response)
        except Exception as e:
            logger.error(f"❌ Error in LLM entity validation: {e}")
            return {"missing_entities": [], "confidence": 0.5, "temporal_intent": "none"}
    
    def _llm_entity_extraction(self, query: str, conversation_history: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """LLM-based entity extraction completa."""
        if not self.llm_provider:
            logger.warning("🔄 LLM provider not available for entity extraction")
            return {"entities": {}, "confidence": 0.0}
            
        history_text = self._format_conversation_history(conversation_history)
        
        prompt = f"""Extraia TODAS as entidades relevantes desta query de análise de dados cambiais:

QUERY: "{query}"
HISTÓRICO: {history_text}

Categorias de entidades:
- TEMPORAL: datas, períodos, anos, meses, "hoje", "ontem", etc.
- BUSINESS: moedas, valores, tipos de operação, clientes
- REFERENCE: "isso", "aquilo", "anterior", "mesmo", etc.
- AGGREGATION: "total", "média", "máximo", "soma", etc.
- FILTER: condições, comparações, filtros específicos

Responda APENAS com JSON:
{{
    "entities": {{
        "temporal": [{{ "value": "texto", "type": "date|period|relative", "confidence": float }}],
        "business": [{{ "value": "texto", "type": "currency|amount|operation", "confidence": float }}],
        "reference": [{{ "value": "texto", "type": "pronoun|demonstrative", "confidence": float }}],
        "aggregation": [{{ "value": "texto", "type": "function", "confidence": float }}],
        "filter": [{{ "value": "texto", "type": "condition", "confidence": float }}]
    }},
    "confidence": float,
    "reasoning": "explicação"
}}"""
        
        try:
            response = self.llm_provider.invoke(prompt)
            return json.loads(response)
        except Exception as e:
            logger.error(f"❌ Error in LLM entity extraction: {e}")
            return {"entities": {}, "confidence": 0.0}
    
    def _llm_temporal_analysis(self, query: str, conversation_history: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Análise temporal especializada via LLM."""
        if not self.llm_provider:
            logger.warning("🔄 LLM provider not available for temporal analysis")
            return {"temporal_intent": "none", "confidence": 0.0}
            
        history_text = self._format_conversation_history(conversation_history)
        
        prompt = f"""Analise a intenção temporal desta query:

QUERY: "{query}"
HISTÓRICO: {history_text}

Determine:
1. Se há intenção temporal explícita, implícita ou nenhuma
2. Que períodos temporais são mencionados ou inferidos
3. Se contexto temporal anterior deve ser preservado

Responda APENAS com JSON:
{{
    "temporal_intent": "explicit|implicit|none",
    "confidence": float,
    "periods_detected": ["lista de períodos identificados"],
    "should_preserve_context": boolean,
    "reasoning": "explicação"
}}"""
        
        try:
            response = self.llm_provider.invoke(prompt)
            return json.loads(response)
        except Exception as e:
            logger.error(f"❌ Error in LLM temporal analysis: {e}")
            return {"temporal_intent": "none", "confidence": 0.0}
    
    def _llm_reference_resolution(self, query: str, conversation_history: Optional[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Resolução de referências via LLM."""
        if not self.llm_provider:
            logger.warning("🔄 LLM provider not available for reference resolution")
            return {"references": [], "needs_context": False, "confidence": 0.0}
            
        history_text = self._format_conversation_history(conversation_history)
        
        prompt = f"""Resolva referências pronominais nesta query:

QUERY: "{query}"
HISTÓRICO: {history_text}

Identifique:
1. Palavras que se referem a elementos anteriores ("isso", "aquilo", "anterior")
2. O que cada referência representa
3. Se contexto anterior é necessário para compreensão

Responda APENAS com JSON:
{{
    "references": [{{ "word": "isso", "refers_to": "descrição", "confidence": float }}],
    "needs_context": boolean,
    "confidence": float,
    "reasoning": "explicação"
}}"""
        
        try:
            response = self.llm_provider.invoke(prompt)
            return json.loads(response)
        except Exception as e:
            logger.error(f"❌ Error in LLM reference resolution: {e}")
            return {"references": [], "needs_context": False, "confidence": 0.0}
    
    def _merge_hardcoded_and_llm(
        self, 
        hardcoded_entities: Dict[str, List[ExtractedEntity]], 
        llm_validation: Dict[str, Any]
    ) -> Dict[str, List[ExtractedEntity]]:
        """Merge resultados hardcoded com validação LLM."""
        merged = hardcoded_entities.copy()
        
        # Add missing entities from LLM validation
        missing_entities = llm_validation.get("missing_entities", [])
        for entity_text in missing_entities:
            # Create ExtractedEntity for missing entities
            entity = ExtractedEntity(
                value=entity_text,
                entity_type="business",  # Default type
                confidence=0.7,  # LLM-suggested entities get medium confidence
                source_span=(0, len(entity_text)),
                context={}
            )
            
            if "business" not in merged:
                merged["business"] = []
            merged["business"].append(entity)
        
        return merged
    
    def _synthesize_llm_results(self, results: Dict[str, Dict[str, Any]]) -> Dict[str, List[ExtractedEntity]]:
        """Sintetiza resultados de múltiplas análises LLM."""
        synthesized = {}
        
        # Process entity extraction results
        entity_extraction = results.get("entity_extraction", {})
        entities_data = entity_extraction.get("entities", {})
        
        for entity_type, entities_list in entities_data.items():
            if not entities_list:
                continue
                
            synthesized[entity_type] = []
            for entity_data in entities_list:
                entity = ExtractedEntity(
                    value=entity_data.get("value", ""),
                    entity_type=entity_data.get("type", entity_type),
                    confidence=entity_data.get("confidence", 0.7),
                    source_span=(0, len(entity_data.get("value", ""))),
                    context={"llm_extracted": True}
                )
                synthesized[entity_type].append(entity)
        
        # Enhance with temporal analysis
        temporal_analysis = results.get("temporal_analysis", {})
        if temporal_analysis.get("temporal_intent") != "none":
            periods = temporal_analysis.get("periods_detected", [])
            for period in periods:
                entity = ExtractedEntity(
                    value=period,
                    entity_type="temporal",
                    confidence=temporal_analysis.get("confidence", 0.7),
                    source_span=(0, len(period)),
                    context={"temporal_intent": temporal_analysis.get("temporal_intent")}
                )
                
                if "temporal" not in synthesized:
                    synthesized["temporal"] = []
                synthesized["temporal"].append(entity)
        
        # Add reference resolution
        reference_analysis = results.get("reference_resolution", {})
        references = reference_analysis.get("references", [])
        for ref_data in references:
            entity = ExtractedEntity(
                value=ref_data.get("word", ""),
                entity_type="reference",
                confidence=ref_data.get("confidence", 0.7),
                source_span=(0, len(ref_data.get("word", ""))),
                context={
                    "refers_to": ref_data.get("refers_to", ""),
                    "needs_context": reference_analysis.get("needs_context", False)
                }
            )
            
            if "reference" not in synthesized:
                synthesized["reference"] = []
            synthesized["reference"].append(entity)
        
        return synthesized
    
    def _calculate_hardcoded_confidence(self, entities: Dict[str, List[ExtractedEntity]]) -> float:
        """Calcula confidence para detecção hardcoded."""
        if not entities:
            return 0.3
        
        total_entities = sum(len(entity_list) for entity_list in entities.values())
        avg_confidence = sum(
            entity.confidence 
            for entity_list in entities.values() 
            for entity in entity_list
        ) / max(total_entities, 1)
        
        # Boost confidence for multiple entities
        entity_boost = min(0.2, total_entities * 0.05)
        
        return min(1.0, avg_confidence + entity_boost)
    
    def _calculate_hybrid_confidence(
        self, 
        hardcoded_entities: Dict[str, List[ExtractedEntity]], 
        llm_validation: Dict[str, Any]
    ) -> float:
        """Calcula confidence para abordagem híbrida."""
        hardcoded_confidence = self._calculate_hardcoded_confidence(hardcoded_entities)
        llm_confidence = llm_validation.get("confidence", 0.5)
        
        # Weighted average favoring LLM validation
        return (hardcoded_confidence * 0.4) + (llm_confidence * 0.6)
    
    def _calculate_llm_confidence(self, results: Dict[str, Dict[str, Any]]) -> float:
        """Calcula confidence para análise LLM completa."""
        confidences = []
        
        for analysis_name, result in results.items():
            confidence = result.get("confidence", 0.0)
            if confidence > 0:
                confidences.append(confidence)
        
        if not confidences:
            return 0.5
        
        # Average confidence with boost for multiple successful analyses
        avg_confidence = sum(confidences) / len(confidences)
        analysis_boost = min(0.1, len(confidences) * 0.03)
        
        return min(1.0, avg_confidence + analysis_boost)
    
    def _format_conversation_history(self, conversation_history: Optional[List[Dict[str, Any]]]) -> str:
        """Formata histórico para prompts LLM."""
        if not conversation_history:
            return "Nenhum histórico disponível"
        
        formatted = []
        for i, interaction in enumerate(conversation_history[-3:]):  # Last 3 interactions
            question = interaction.get("question", "")
            response = interaction.get("response", "")
            formatted.append(f"Q{i+1}: {question[:100]}")
            if response:
                formatted.append(f"R{i+1}: {response[:100]}")
        
        return "\n".join(formatted)
    
    def _generate_cache_key(self, query: str, conversation_history: Optional[List[Dict[str, Any]]]) -> str:
        """Gera chave de cache para a análise."""
        history_hash = ""
        if conversation_history:
            history_text = str(conversation_history[-2:])  # Last 2 interactions
            history_hash = hashlib.md5(history_text.encode()).hexdigest()[:8]
        
        query_hash = hashlib.md5(query.encode()).hexdigest()[:12]
        return f"ctx_{query_hash}_{history_hash}"
    
    def _get_from_cache(self, cache_key: str) -> Optional[ContextAnalysisResult]:
        """Recupera resultado do cache."""
        if cache_key not in self.analysis_cache:
            return None
        
        cached_data, timestamp = self.analysis_cache[cache_key]
        
        # Check TTL
        if time.time() - timestamp > self.cache_ttl:
            del self.analysis_cache[cache_key]
            return None
        
        return cached_data
    
    def _cache_result(self, cache_key: str, result: ContextAnalysisResult):
        """Armazena resultado no cache."""
        # Cleanup cache if too large
        if len(self.analysis_cache) >= self.cache_max_size:
            # Remove oldest entries
            sorted_items = sorted(
                self.analysis_cache.items(), 
                key=lambda x: x[1][1]  # Sort by timestamp
            )
            for old_key, _ in sorted_items[:50]:  # Remove 50 oldest
                del self.analysis_cache[old_key]
        
        self.analysis_cache[cache_key] = (result, time.time())
    
    def _fallback_detection(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]],
        start_time: float
    ) -> ContextAnalysisResult:
        """Fallback para hardcoded extraction em caso de erro."""
        logger.warning("🔄 Using fallback hardcoded detection")
        
        try:
            entities = self.hardcoded_extractor.extract_entities(query, conversation_history)
            confidence = self._calculate_hardcoded_confidence(entities)
        except Exception as e:
            logger.error(f"❌ Fallback detection failed: {e}")
            entities = {}
            confidence = 0.1
        
        return ContextAnalysisResult(
            entities=entities,
            confidence=confidence,
            strategy_used=DetectionStrategy.HARDCODED,
            processing_time=time.time() - start_time,
            llm_calls_made=0,
            cache_hit=False,
            complexity_level=ContextComplexity.SIMPLE,
            reasoning="Fallback to hardcoded extraction due to error",
            metadata={"fallback": True, "error_recovery": True}
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do detector."""
        return {
            "cache_size": len(self.analysis_cache),
            "cache_max_size": self.cache_max_size,
            "cache_ttl": self.cache_ttl,
            "max_workers": self.max_workers,
            "complexity_patterns": len(self.complexity_patterns),
            "initialized": True
        }


# Utility functions para integração
def detect_intelligent_context(
    query: str,
    conversation_history: Optional[List[Dict[str, Any]]] = None,
    sector: str = "cambio",
    client_id: str = "L2M"
) -> ContextAnalysisResult:
    """
    Função de conveniência para detecção inteligente de contexto.
    
    Args:
        query: Query do usuário
        conversation_history: Histórico da conversa
        sector: Setor do cliente
        client_id: ID do cliente
        
    Returns:
        ContextAnalysisResult: Resultado da análise
    """
    detector = IntelligentContextDetector(sector, client_id)
    return detector.detect_context(query, conversation_history)


if __name__ == "__main__":
    # Test cases para validação
    test_cases = [
        {
            "query": "Qual o total de vendas hoje?",
            "expected_complexity": ContextComplexity.SIMPLE,
            "expected_strategy": DetectionStrategy.HARDCODED
        },
        {
            "query": "Compare as vendas de 2023 com 2024 e explique a diferença",
            "expected_complexity": ContextComplexity.COMPLEX,
            "expected_strategy": DetectionStrategy.FULL_LLM
        },
        {
            "query": "Isso está errado, deveria mostrar operações de abril",
            "expected_complexity": ContextComplexity.MODERATE,
            "expected_strategy": DetectionStrategy.HYBRID
        }
    ]
    
    detector = IntelligentContextDetector()
    
    for i, case in enumerate(test_cases):
        print(f"\n🧪 Test Case {i+1}: {case['query']}")
        
        result = detector.detect_context(case["query"])
        
        print(f"Complexity: {result.complexity_level} (expected: {case['expected_complexity']})")
        print(f"Strategy: {result.strategy_used} (expected: {case['expected_strategy']})")
        print(f"Confidence: {result.confidence:.2f}")
        print(f"Processing time: {result.processing_time:.3f}s")
        print(f"LLM calls: {result.llm_calls_made}")
        print(f"Entities found: {sum(len(v) for v in result.entities.values())}")