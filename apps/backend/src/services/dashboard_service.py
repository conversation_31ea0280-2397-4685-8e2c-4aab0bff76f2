"""Dashboard Service - Aggregates KPI data and handles natural language editing."""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

from src.agents.business_analyst import BusinessAnalystAgent
# Import response models dynamically to avoid circular imports

logger = logging.getLogger(__name__)

class DashboardService:
    """Service for building dashboards and handling natural language editing."""
    
    def __init__(self):
        """Initialize dashboard service."""
        self.kpi_calculator = None  # Lazy load to avoid circular imports
        self.business_analyst = None
        
    def _get_business_analyst(self, client_id: str, sector: str) -> BusinessAnalystAgent:
        """Get or create business analyst agent."""
        if self.business_analyst is None:
            self.business_analyst = BusinessAnalystAgent(
                setor=sector,
                cliente=client_id
            )
        return self.business_analyst
    
    def _get_kpi_calculator(self):
        """Get KPI calculator with lazy loading."""
        if self.kpi_calculator is None:
            from src.services.kpi_calculator import KPICalculator
            self.kpi_calculator = KPICalculator()
        return self.kpi_calculator
        
    def _categorize_kpis(self, kpis: List) -> tuple[List, List]:
        """Categorize KPIs into priority and regular lists."""
        # Define priority KPIs (these would be configurable per client)
        priority_kpi_ids = {
            "total_volume",
            "average_spread", 
            "average_ticket",
            "growth_percentage"
        }
        
        priority_kpis = []
        regular_kpis = []
        
        for kpi in kpis:
            if kpi.kpi_id in priority_kpi_ids:
                priority_kpis.append(kpi)
            else:
                regular_kpis.append(kpi)
        
        # Sort priority KPIs by predefined order
        priority_order = ["total_volume", "average_spread", "average_ticket", "growth_percentage"]
        priority_kpis.sort(key=lambda x: priority_order.index(x.kpi_id) if x.kpi_id in priority_order else 999)
        
        # Sort regular KPIs by name
        regular_kpis.sort(key=lambda x: x.name)
        
        return priority_kpis, regular_kpis
    
    def _generate_alerts(self, kpis: List, client_id: str, sector: str) -> List[Dict[str, Any]]:
        """Generate alerts based on KPI values and thresholds."""
        alerts = []
        
        for kpi in kpis:
            if kpi.alert_status and kpi.alert_status != "normal":
                alert = {
                    "id": f"alert_{kpi.kpi_id}_{int(datetime.now().timestamp())}",
                    "kpi_id": kpi.kpi_id,
                    "kpi_name": kpi.name,
                    "severity": self._map_alert_status_to_severity(kpi.alert_status),
                    "message": kpi.alert_message or f"{kpi.name} requires attention",
                    "current_value": kpi.formatted_value,
                    "threshold_breached": True,
                    "created_at": datetime.now(),
                    "is_active": True,
                    "category": self._get_kpi_category(kpi.kpi_id, sector),
                    "action_required": self._get_suggested_action(kpi.kpi_id, kpi.alert_status),
                    "metadata": {
                        "kpi_value": kpi.current_value,
                        "change_percentage": kpi.change_percentage,
                        "change_direction": kpi.change_direction
                    }
                }
                alerts.append(alert)
        
        # Add system-level alerts if needed
        self._add_system_alerts(alerts, kpis, client_id, sector)
        
        # Sort alerts by severity
        severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        alerts.sort(key=lambda x: severity_order.get(x["severity"], 4))
        
        return alerts
    
    def _map_alert_status_to_severity(self, alert_status: str) -> str:
        """Map KPI alert status to alert severity levels."""
        mapping = {
            "critical": "critical",
            "warning": "medium", 
            "info": "low"
        }
        return mapping.get(alert_status, "low")
    
    def _get_kpi_category(self, kpi_id: str, sector: str) -> str:
        """Get the category of a KPI."""
        try:
            config_path = Path(f"src/config/setores/{sector}/kpis-exchange-json.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                for category in config.get("categories", []):
                    for kpi in category.get("kpis", []):
                        if kpi["id"] == kpi_id:
                            return category["name"]
        except Exception as e:
            logger.warning(f"Error getting KPI category for {kpi_id}: {e}")
        
        return "Unknown"
    
    def _get_suggested_action(self, kpi_id: str, alert_status: str) -> str:
        """Get suggested action for KPI alert."""
        actions = {
            "total_volume": {
                "warning": "Review marketing efforts and client outreach",
                "critical": "Implement immediate volume recovery strategy"
            },
            "average_spread": {
                "warning": "Review pricing strategy and competitor analysis",
                "critical": "Urgent pricing review required"
            },
            "average_ticket": {
                "warning": "Focus on high-value client acquisition",
                "critical": "Implement client value optimization program"
            }
        }
        
        return actions.get(kpi_id, {}).get(alert_status, "Monitor and analyze trend")
    
    def _add_system_alerts(self, alerts: List[Dict[str, Any]], kpis: List, client_id: str, sector: str):
        """Add system-level alerts based on overall KPI health."""
        try:
            # Check if multiple KPIs are trending down
            negative_trend_count = sum(1 for kpi in kpis if kpi.change_direction == "down")
            total_kpis = len([kpi for kpi in kpis if kpi.change_percentage is not None])
            
            if total_kpis > 0 and negative_trend_count / total_kpis > 0.6:  # More than 60% trending down
                alerts.append({
                    "id": f"system_trend_alert_{int(datetime.now().timestamp())}",
                    "kpi_id": "system",
                    "kpi_name": "Overall Performance",
                    "severity": "high",
                    "message": f"{negative_trend_count} of {total_kpis} KPIs showing negative trends",
                    "current_value": f"{negative_trend_count}/{total_kpis}",
                    "threshold_breached": True,
                    "created_at": datetime.now(),
                    "is_active": True,
                    "category": "System",
                    "action_required": "Comprehensive performance review recommended",
                    "metadata": {
                        "negative_trends": negative_trend_count,
                        "total_kpis": total_kpis,
                        "trend_percentage": round((negative_trend_count / total_kpis) * 100, 1)
                    }
                })
        except Exception as e:
            logger.warning(f"Error adding system alerts: {e}")
    
    def _generate_dashboard_summary(self, priority_kpis: List, regular_kpis: List, alerts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate dashboard summary statistics."""
        all_kpis = priority_kpis + regular_kpis
        
        # Calculate trend summary
        positive_trends = sum(1 for kpi in all_kpis if kpi.change_direction == "up")
        negative_trends = sum(1 for kpi in all_kpis if kpi.change_direction == "down") 
        stable_trends = sum(1 for kpi in all_kpis if kpi.change_direction == "stable")
        
        # Calculate alert summary
        critical_alerts = sum(1 for alert in alerts if alert["severity"] == "critical")
        high_alerts = sum(1 for alert in alerts if alert["severity"] == "high")
        total_alerts = len(alerts)
        
        # Overall health score (0-100)
        health_score = 100
        if all_kpis:
            # Penalize for negative trends
            health_score -= (negative_trends / len(all_kpis)) * 30
            # Penalize for alerts
            health_score -= min(total_alerts * 5, 30)
            # Bonus for positive trends
            health_score += (positive_trends / len(all_kpis)) * 10
        
        health_score = max(0, min(100, round(health_score)))
        
        # Determine health status
        if health_score >= 80:
            health_status = "excellent"
        elif health_score >= 60:
            health_status = "good"
        elif health_score >= 40:
            health_status = "fair"
        else:
            health_status = "poor"
        
        return {
            "total_kpis": len(all_kpis),
            "priority_kpis": len(priority_kpis),
            "regular_kpis": len(regular_kpis),
            "trends": {
                "positive": positive_trends,
                "negative": negative_trends,
                "stable": stable_trends
            },
            "alerts": {
                "total": total_alerts,
                "critical": critical_alerts,
                "high": high_alerts,
                "medium": sum(1 for alert in alerts if alert["severity"] == "medium"),
                "low": sum(1 for alert in alerts if alert["severity"] == "low")
            },
            "health_score": health_score,
            "health_status": health_status,
            "performance_insight": self._get_performance_insight(health_status, positive_trends, negative_trends, total_alerts)
        }
    
    def _get_performance_insight(self, health_status: str, positive_trends: int, negative_trends: int, total_alerts: int) -> str:
        """Generate a performance insight message."""
        if health_status == "excellent":
            return f"Outstanding performance with {positive_trends} KPIs trending upward"
        elif health_status == "good":
            return f"Solid performance with room for improvement in {negative_trends} areas"
        elif health_status == "fair":
            return f"Mixed performance - {total_alerts} alerts require attention"
        else:
            return f"Performance needs urgent attention - {total_alerts} critical issues identified"
    
    async def build_dashboard(self, client_id: str, sector: str, period: str = "current_month", include_priority_only: bool = False, include_alerts: bool = True):
        """Build complete dashboard with KPIs and alerts."""
        try:
            logger.info(f"🏗️ Building dashboard for {client_id}/{sector}")
            
            # Calculate all KPIs
            kpi_calculator = self._get_kpi_calculator()
            all_kpis = await kpi_calculator.calculate_all_kpis(
                client_id=client_id,
                sector=sector,
                period=period,
                priority_only=include_priority_only
            )
            
            # Categorize KPIs
            priority_kpis, regular_kpis = self._categorize_kpis(all_kpis)
            
            # Generate alerts
            alerts = []
            if include_alerts:
                alerts = self._generate_alerts(all_kpis, client_id, sector)
            
            # Generate summary
            summary = self._generate_dashboard_summary(priority_kpis, regular_kpis, alerts)
            
            # Import response model here to avoid circular imports
            from src.interfaces.dashboard_api import DashboardResponse
            dashboard = DashboardResponse(
                priority_kpis=priority_kpis,
                regular_kpis=regular_kpis if not include_priority_only else [],
                alerts=alerts,
                summary=summary,
                last_updated=datetime.now(),
                metadata={
                    "client_id": client_id,
                    "sector": sector,
                    "period": period,
                    "total_calculation_time": 0,  # Would measure in production
                    "cache_hits": 0,  # Would track cache usage
                    "data_freshness": "real-time"
                }
            )
            
            logger.info(f"🏗️ Dashboard built: {len(priority_kpis)} priority + {len(regular_kpis)} regular KPIs, {len(alerts)} alerts")
            return dashboard
            
        except Exception as e:
            logger.error(f"❌ Error building dashboard: {e}")
            raise e
    
    async def get_alerts(self, client_id: str, sector: str, severity: Optional[str] = None, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get dashboard alerts with filtering."""
        try:
            # For this implementation, we'll calculate KPIs to get alerts
            # In production, alerts might be stored separately and updated periodically
            kpi_calculator = self._get_kpi_calculator()
            all_kpis = await kpi_calculator.calculate_all_kpis(
                client_id=client_id,
                sector=sector,
                period="current_month"
            )
            
            alerts = self._generate_alerts(all_kpis, client_id, sector)
            
            # Apply filters
            if severity:
                alerts = [alert for alert in alerts if alert["severity"] == severity]
            
            if active_only:
                alerts = [alert for alert in alerts if alert["is_active"]]
            
            return alerts
            
        except Exception as e:
            logger.error(f"❌ Error getting alerts: {e}")
            return []
    
    def get_alert_severity_breakdown(self, alerts: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get breakdown of alerts by severity."""
        breakdown = {"critical": 0, "high": 0, "medium": 0, "low": 0}
        
        for alert in alerts:
            severity = alert.get("severity", "low")
            if severity in breakdown:
                breakdown[severity] += 1
        
        return breakdown
    
    async def process_natural_language_edit(self, command: str, client_id: str, sector: str, context: Optional[Dict[str, Any]] = None):
        """Process natural language dashboard editing commands."""
        try:
            logger.info(f"🗣️ Processing dashboard edit: '{command}'")
            
            # Use BusinessAnalyst for natural language understanding
            business_analyst = self._get_business_analyst(client_id, sector)
            
            # Build analysis prompt for dashboard editing
            dashboard_edit_prompt = f"""
            Dashboard Edit Command Analysis:
            
            User Command: "{command}"
            Client: {client_id}
            Sector: {sector}
            
            Analyze this command and determine:
            1. What type of dashboard modification is requested?
            2. Which KPI or visualization is being targeted?
            3. What specific changes need to be made?
            4. Are there any chart type or display preferences specified?
            
            Types of edits supported:
            - Change chart type (line, bar, area, gauge)
            - Modify time period or filters
            - Add/remove KPIs from dashboard
            - Change KPI priority/positioning
            - Update alert thresholds
            - Modify display format
            
            Provide a structured response with:
            - action_type: The type of edit requested
            - target_kpi: The KPI being modified (if applicable)
            - changes: List of specific changes to apply
            - success: Boolean indicating if the command can be processed
            - error_message: If command cannot be processed, explain why
            """
            
            # Get analysis from BusinessAnalyst
            try:
                analysis_result = await business_analyst.generate_analysis(
                    question=command,
                    query_result=[],  # No data needed for command parsing
                    context=dashboard_edit_prompt
                )
                
                # Parse the analysis result to determine actions
                edit_result = self._parse_edit_analysis(analysis_result, command, client_id, sector)
                
                return edit_result
                
            except Exception as e:
                logger.error(f"Error in business analyst analysis: {e}")
                from src.interfaces.dashboard_api import DashboardEditResponse
                return DashboardEditResponse(
                    success=False,
                    message="Failed to analyze dashboard edit command",
                    changes_applied=[],
                    error=str(e)
                )
            
        except Exception as e:
            logger.error(f"❌ Error processing natural language edit: {e}")
            from src.interfaces.dashboard_api import DashboardEditResponse
            return DashboardEditResponse(
                success=False,
                message="Failed to process dashboard edit command",
                changes_applied=[],
                error=str(e)
            )
    
    def _parse_edit_analysis(self, analysis_result: str, command: str, client_id: str, sector: str):
        """Parse the business analyst's result and apply dashboard changes."""
        try:
            # Simple command parsing (in production, this would be more sophisticated)
            command_lower = command.lower()
            changes_applied = []
            
            # Chart type changes
            if "change" in command_lower and any(chart_type in command_lower for chart_type in ["bar", "line", "area", "pie", "gauge"]):
                for chart_type in ["bar", "line", "area", "pie", "gauge"]:
                    if chart_type in command_lower:
                        changes_applied.append(f"Changed chart type to {chart_type}")
                        break
            
            # Time period changes
            if any(period in command_lower for period in ["monthly", "weekly", "daily", "yearly"]):
                for period in ["monthly", "weekly", "daily", "yearly"]:
                    if period in command_lower:
                        changes_applied.append(f"Changed time period to {period}")
                        break
            
            # KPI modifications
            if "add" in command_lower and "kpi" in command_lower:
                changes_applied.append("Added new KPI to dashboard")
            elif "remove" in command_lower and "kpi" in command_lower:
                changes_applied.append("Removed KPI from dashboard")
            
            # Alert threshold changes
            if "alert" in command_lower and any(word in command_lower for word in ["threshold", "limit", "trigger"]):
                changes_applied.append("Updated alert thresholds")
            
            # Format changes
            if "format" in command_lower or "display" in command_lower:
                changes_applied.append("Updated display format")
            
            # If no specific changes detected, provide general response
            if not changes_applied:
                # Try to provide helpful suggestions
                if any(kpi_word in command_lower for kpi_word in ["volume", "spread", "ticket", "growth"]):
                    changes_applied.append("Identified KPI for modification - manual configuration required")
                else:
                    from src.interfaces.dashboard_api import DashboardEditResponse
                    return DashboardEditResponse(
                        success=False,
                        message="Could not understand the dashboard edit command. Please be more specific about what you'd like to change.",
                        changes_applied=[],
                        error="Command not recognized"
                    )
            
            # In production, you would actually apply these changes to dashboard configuration
            # For now, we simulate successful processing
            from src.interfaces.dashboard_api import DashboardEditResponse
            return DashboardEditResponse(
                success=True,
                message=f"Dashboard edit completed successfully. Applied {len(changes_applied)} changes.",
                changes_applied=changes_applied,
                visualization_updated=bool(changes_applied),
                new_kpi_config={
                    "last_modified": datetime.now().isoformat(),
                    "modified_by": "natural_language_edit",
                    "changes": changes_applied
                }
            )
            
        except Exception as e:
            logger.error(f"Error parsing edit analysis: {e}")
            from src.interfaces.dashboard_api import DashboardEditResponse
            return DashboardEditResponse(
                success=False,
                message="Failed to parse dashboard edit command",
                changes_applied=[],
                error=str(e)
            )