"""
Snapshot Service for DataHero4 MVP
==================================

Gera snapshots diários dos KPIs críticos para performance otimizada.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import time

from src.config.critical_kpis import CriticalKpisConfig
from src.services.kpi_service import get_kpi_service
from src.models.learning_models import KpiDefinition
from src.utils.learning_db_utils import get_db_manager
from src.utils.logging_utils import (
    log_snapshot_start,
    log_snapshot_success,
    log_snapshot_failure,
    log_kpi_result
)
from src.utils.alert_system import (
    alert_snapshot_failure,
    alert_low_success_rate,
    alert_stale_snapshot
)

logger = logging.getLogger(__name__)


class SnapshotService:
    """Serviço para geração e gestão de snapshots de KPIs."""
    
    def __init__(self):
        self.kpi_service = get_kpi_service()
        self.db_manager = get_db_manager()
        self.snapshot_dir = Path("data/snapshots")
        self.snapshot_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_daily_snapshot(self, client_id: str = "L2M") -> Dict[str, Any]:
        """
        Gera snapshot diário com os 6 KPIs críticos.

        Args:
            client_id: Identificador do cliente

        Returns:
            Dict com snapshot gerado
        """
        start_time = time.time()
        logger.info(f"🔄 Iniciando geração de snapshot para cliente {client_id}")
        log_snapshot_start(client_id)

        try:
            # 1. Buscar definições dos KPIs críticos no banco
            critical_kpis = self._get_critical_kpi_definitions()

            if not critical_kpis:
                logger.error("❌ Nenhum KPI crítico encontrado no banco!")
                # Tentar fallback com definições hardcoded
                critical_kpis = self._get_fallback_kpi_definitions()
                if not critical_kpis:
                    return self._generate_error_snapshot("No critical KPIs found and fallback failed")
                logger.warning("⚠️ Usando definições de fallback para KPIs críticos")

            logger.info(f"📊 Encontrados {len(critical_kpis)} KPIs críticos")

            # 2. Calcular valor de cada KPI usando o serviço existente
            kpi_data = {}
            failed_kpis = []

            for kpi_def in critical_kpis:
                try:
                    kpi_start_time = time.time()
                    logger.info(f"🧮 Calculando KPI: {kpi_def['id']}")

                    # Usar método existente do KpiCalculationService com timeout
                    kpi_result = self._calculate_kpi_with_timeout(kpi_def, client_id)
                    kpi_duration = time.time() - kpi_start_time

                    if kpi_result and kpi_result.get('currentValue') is not None:
                        # Formatar para snapshot
                        kpi_data[kpi_def['id']] = {
                            'value': kpi_result['currentValue'],
                            'formatted': self._format_kpi_value(
                                kpi_result['currentValue'],
                                kpi_def['format_type']
                            ),
                            'title': kpi_def['name'],
                            'description': kpi_def['description'],
                            'icon': self._get_kpi_icon(kpi_def['id']),
                            'format': kpi_def['format_type'],
                            'unit': kpi_def.get('unit', ''),
                            'category': kpi_def['category'],
                            'status': 'success'
                        }

                        # Log sucesso do KPI
                        log_kpi_result(
                            kpi_def['id'],
                            True,
                            value=kpi_result['currentValue'],
                            duration_seconds=kpi_duration
                        )
                    else:
                        logger.warning(f"⚠️ Falha ao calcular KPI: {kpi_def['id']}")
                        failed_kpis.append(kpi_def['id'])

                        # Log falha do KPI
                        log_kpi_result(
                            kpi_def['id'],
                            False,
                            duration_seconds=kpi_duration,
                            error="Calculation returned null or invalid value"
                        )

                        # Adicionar placeholder com erro
                        kpi_data[kpi_def['id']] = {
                            'value': None,
                            'formatted': 'N/A',
                            'title': kpi_def['name'],
                            'description': kpi_def['description'],
                            'icon': self._get_kpi_icon(kpi_def['id']),
                            'format': kpi_def['format_type'],
                            'unit': kpi_def.get('unit', ''),
                            'category': kpi_def['category'],
                            'status': 'error',
                            'error': 'Calculation failed'
                        }

                except Exception as kpi_error:
                    logger.error(f"❌ Erro ao calcular KPI {kpi_def['id']}: {kpi_error}")
                    failed_kpis.append(kpi_def['id'])

                    # Adicionar placeholder com erro específico
                    kpi_data[kpi_def['id']] = {
                        'value': None,
                        'formatted': 'N/A',
                        'title': kpi_def.get('name', kpi_def['id']),
                        'description': kpi_def.get('description', ''),
                        'icon': self._get_kpi_icon(kpi_def['id']),
                        'format': kpi_def.get('format_type', 'number'),
                        'unit': kpi_def.get('unit', ''),
                        'category': kpi_def.get('category', 'unknown'),
                        'status': 'error',
                        'error': str(kpi_error)
                    }

            # 3. Criar estrutura do snapshot
            successful_kpis = len([k for k in kpi_data.values() if k.get('status') != 'error'])

            snapshot = {
                "metadata": {
                    "date": datetime.now().strftime("%Y-%m-%d"),
                    "time": datetime.now().strftime("%H:%M:%S"),
                    "client_id": client_id,
                    "version": "1.0",
                    "kpi_count": len(kpi_data),
                    "successful_kpis": successful_kpis,
                    "failed_kpis": failed_kpis
                },
                "kpis": kpi_data,
                "summary": {
                    "total_calculated": successful_kpis,
                    "total_expected": len(critical_kpis),
                    "total_failed": len(failed_kpis),
                    "success_rate": (successful_kpis / len(critical_kpis) * 100) if critical_kpis else 0
                }
            }

            # 4. Salvar snapshot
            self._save_snapshot(snapshot)

            # Log resultado final
            duration = time.time() - start_time
            success_rate = (successful_kpis / len(critical_kpis) * 100) if critical_kpis else 0

            if failed_kpis:
                logger.warning(f"⚠️ Snapshot gerado com {successful_kpis}/{len(critical_kpis)} KPIs. Falhas: {failed_kpis}")
            else:
                logger.info(f"✅ Snapshot gerado com sucesso: {successful_kpis} KPIs")

            # Log estruturado do resultado
            log_snapshot_success(client_id, successful_kpis, success_rate, duration)

            # Verificar se precisa de alertas
            if success_rate < 80:
                alert_low_success_rate(success_rate, client_id)

            return snapshot

        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"❌ Erro crítico ao gerar snapshot: {e}", exc_info=True)
            log_snapshot_failure(client_id, str(e), duration)
            alert_snapshot_failure(client_id, str(e), duration)
            return self._generate_error_snapshot(str(e))
    
    def _get_critical_kpi_definitions(self) -> List[Dict[str, Any]]:
        """Busca definições dos KPIs críticos no banco."""
        try:
            if not self.db_manager:
                logger.warning("⚠️ Database manager não disponível")
                return []

            with self.db_manager.get_session() as session:
                # Buscar apenas KPIs críticos
                critical_ids = CriticalKpisConfig.get_critical_kpi_ids()

                kpis = session.query(KpiDefinition).filter(
                    KpiDefinition.id.in_(critical_ids),
                    KpiDefinition.is_active == True
                ).order_by(KpiDefinition.display_order).all()

                # Converter para dict dentro da sessão
                return [kpi.to_dict() for kpi in kpis]

        except Exception as e:
            logger.error(f"Erro ao buscar KPIs críticos: {e}")
            return []

    def _get_fallback_kpi_definitions(self) -> List[Dict[str, Any]]:
        """Retorna definições de fallback para KPIs críticos quando o banco não está disponível."""
        logger.info("📋 Usando definições de fallback para KPIs críticos")

        metadata = CriticalKpisConfig.get_kpi_metadata()
        fallback_definitions = []

        for kpi_id, meta in metadata.items():
            fallback_definitions.append({
                'id': kpi_id,
                'name': meta['name'],
                'description': meta['description'],
                'category': meta['category'],
                'format_type': meta['format_type'],
                'unit': '',
                'is_active': True,
                'display_order': meta['priority']
            })

        return fallback_definitions

    def _calculate_kpi_with_timeout(self, kpi_def: Dict[str, Any], client_id: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Calcula KPI com timeout para evitar travamentos.

        Args:
            kpi_def: Definição do KPI
            client_id: ID do cliente
            timeout: Timeout em segundos

        Returns:
            Resultado do cálculo ou None se falhar
        """
        try:
            # Por enquanto, usar o método direto
            # Em produção, poderia usar threading.Timer ou asyncio.wait_for
            return self.kpi_service.calculate_kpi_value_from_dict(
                kpi_dict=kpi_def,
                client_id=client_id
            )
        except Exception as e:
            logger.error(f"Timeout ou erro ao calcular KPI {kpi_def['id']}: {e}")
            return None

    def _format_kpi_value(self, value: float, format_type: str) -> str:
        """Formata valor do KPI para exibição."""
        if value is None:
            return "N/A"

        if format_type == "currency":
            # Formatar como moeda brasileira
            if value >= 1e9:
                return f"R$ {value/1e9:.1f}B"
            elif value >= 1e6:
                return f"R$ {value/1e6:.1f}M"
            else:
                return f"R$ {value:,.0f}"

        elif format_type == "percentage":
            return f"{value:.1f}%"

        elif format_type == "number":
            if value >= 1000:
                return f"{value:,.0f}"
            else:
                return f"{value:.0f}"

        else:
            return str(value)

    def _get_kpi_icon(self, kpi_id: str) -> str:
        """Retorna ícone apropriado para cada KPI."""
        icon_map = {
            "total_volume": "TrendingUp",
            "average_spread": "DollarSign",
            "conversion_rate": "Target",
            "average_ticket": "CreditCard",
            "retention_rate": "Users",
            "operations_per_analyst": "Activity"
        }
        return icon_map.get(kpi_id, "BarChart")

    def _save_snapshot(self, snapshot: Dict[str, Any]):
        """Salva snapshot em arquivo JSON."""
        # Nome do arquivo com data
        filename = f"snapshot_{datetime.now().strftime('%Y%m%d')}.json"
        filepath = self.snapshot_dir / filename

        # Salvar snapshot datado
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(snapshot, f, indent=2, ensure_ascii=False)

        # Salvar também como "latest" para acesso rápido
        latest_path = self.snapshot_dir / "latest.json"
        with open(latest_path, 'w', encoding='utf-8') as f:
            json.dump(snapshot, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Snapshot salvo: {filepath}")

    def get_latest_snapshot(self) -> Optional[Dict[str, Any]]:
        """Retorna o snapshot mais recente."""
        latest_path = self.snapshot_dir / "latest.json"

        if latest_path.exists():
            with open(latest_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        return None

    def _generate_error_snapshot(self, error_msg: str) -> Dict[str, Any]:
        """Gera snapshot de erro para fallback."""
        return {
            "metadata": {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "time": datetime.now().strftime("%H:%M:%S"),
                "error": True,
                "error_message": error_msg,
                "version": "1.0",
                "kpi_count": 0
            },
            "kpis": {},
            "summary": {
                "total_calculated": 0,
                "total_expected": 6,
                "total_failed": 6,
                "success_rate": 0
            }
        }

    def validate_snapshot(self, snapshot: Dict[str, Any]) -> bool:
        """
        Valida se um snapshot está bem formado.

        Args:
            snapshot: Snapshot para validar

        Returns:
            True se válido, False caso contrário
        """
        try:
            # Verificar estrutura básica
            required_keys = ['metadata', 'kpis', 'summary']
            if not all(key in snapshot for key in required_keys):
                logger.error("❌ Snapshot inválido: chaves obrigatórias ausentes")
                return False

            # Verificar metadata
            metadata = snapshot['metadata']
            required_metadata = ['date', 'time', 'version']
            if not all(key in metadata for key in required_metadata):
                logger.error("❌ Snapshot inválido: metadata incompleta")
                return False

            # Verificar summary
            summary = snapshot['summary']
            required_summary = ['total_calculated', 'total_expected', 'success_rate']
            if not all(key in summary for key in required_summary):
                logger.error("❌ Snapshot inválido: summary incompleta")
                return False

            # Verificar se success_rate está entre 0 e 100
            if not 0 <= summary['success_rate'] <= 100:
                logger.error("❌ Snapshot inválido: success_rate fora do intervalo válido")
                return False

            # Verificar KPIs
            kpis = snapshot['kpis']
            if not isinstance(kpis, dict):
                logger.error("❌ Snapshot inválido: kpis deve ser um dicionário")
                return False

            # Verificar estrutura de cada KPI
            for kpi_id, kpi_data in kpis.items():
                required_kpi_keys = ['value', 'formatted', 'title', 'format']
                if not all(key in kpi_data for key in required_kpi_keys):
                    logger.error(f"❌ Snapshot inválido: KPI {kpi_id} com estrutura incompleta")
                    return False

            logger.debug("✅ Snapshot validado com sucesso")
            return True

        except Exception as e:
            logger.error(f"❌ Erro ao validar snapshot: {e}")
            return False

    def get_snapshot_health(self) -> Dict[str, Any]:
        """
        Retorna informações sobre a saúde do sistema de snapshots.

        Returns:
            Dict com informações de saúde
        """
        try:
            latest_snapshot = self.get_latest_snapshot()

            if not latest_snapshot:
                return {
                    "status": "unhealthy",
                    "message": "No snapshot available",
                    "last_generation": None,
                    "success_rate": 0
                }

            # Verificar se snapshot é válido
            is_valid = self.validate_snapshot(latest_snapshot)

            # Verificar idade do snapshot
            snapshot_date = latest_snapshot['metadata']['date']
            snapshot_time = latest_snapshot['metadata']['time']
            snapshot_datetime = datetime.strptime(f"{snapshot_date} {snapshot_time}", "%Y-%m-%d %H:%M:%S")
            age_hours = (datetime.now() - snapshot_datetime).total_seconds() / 3600

            # Determinar status
            if not is_valid:
                status = "unhealthy"
                message = "Invalid snapshot structure"
            elif age_hours > 25:  # Mais de 25 horas
                status = "stale"
                message = f"Snapshot is {age_hours:.1f} hours old"
            elif latest_snapshot['summary']['success_rate'] < 50:
                status = "degraded"
                message = f"Low success rate: {latest_snapshot['summary']['success_rate']:.1f}%"
            else:
                status = "healthy"
                message = "Snapshot system operating normally"

            return {
                "status": status,
                "message": message,
                "last_generation": f"{snapshot_date} {snapshot_time}",
                "age_hours": round(age_hours, 1),
                "success_rate": latest_snapshot['summary']['success_rate'],
                "kpi_count": latest_snapshot['metadata'].get('kpi_count', 0),
                "is_valid": is_valid
            }

        except Exception as e:
            logger.error(f"❌ Erro ao verificar saúde do snapshot: {e}")
            return {
                "status": "error",
                "message": f"Health check failed: {str(e)}",
                "last_generation": None,
                "success_rate": 0
            }


# Função para uso em scripts e cron jobs
def generate_daily_snapshot_cli():
    """Função CLI para gerar snapshot diário."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    service = SnapshotService()
    snapshot = service.generate_daily_snapshot()

    if snapshot['metadata'].get('error'):
        logger.error(f"❌ Falha na geração do snapshot: {snapshot['metadata']['error_message']}")
        return 1
    else:
        logger.info(f"✅ Snapshot gerado com sucesso: {snapshot['summary']['total_calculated']} KPIs")
        return 0


if __name__ == "__main__":
    import sys
    sys.exit(generate_daily_snapshot_cli())
