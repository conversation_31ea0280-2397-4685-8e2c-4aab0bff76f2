#!/usr/bin/env python3
"""
Test script para validar IntelligentContextDetector
Verifica compatibilidade Railway (sync-only) e performance
"""

import time
import logging
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import sistema inteligente
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.intelligent_context_detector import (
    IntelligentContextDetector, 
    ContextComplexity, 
    DetectionStrategy,
    detect_intelligent_context
)

def test_railway_compatibility():
    """Testa compatibilidade 100% sync para Railway."""
    logger.info("🚀 Testing Railway compatibility (sync-only)...")
    
    detector = IntelligentContextDetector()
    
    # Test cases que causaram problemas antes
    test_cases = [
        {
            "query": "quanto vendemos em 2024",
            "description": "Simple temporal query",
            "expected_complexity": ContextComplexity.SIMPLE
        },
        {
            "query": "e em 2023",
            "description": "Reference to previous period - caused contradictions",
            "expected_complexity": ContextComplexity.MODERATE
        },
        {
            "query": "isso está errado, deveria mostrar operações de abril não março",
            "description": "Correction feedback - needs intelligent analysis",
            "expected_complexity": ContextComplexity.COMPLEX
        },
        {
            "query": "compare vendas entre 2023 e 2024 e explique as diferenças",
            "description": "Complex analysis request",
            "expected_complexity": ContextComplexity.COMPLEX
        }
    ]
    
    for i, case in enumerate(test_cases):
        logger.info(f"\n🧪 Test Case {i+1}: {case['description']}")
        logger.info(f"Query: '{case['query']}'")
        
        try:
            # CRITICAL: Testa execução SYNC (não async!)
            start_time = time.time()
            result = detector.detect_context(case["query"])
            duration = time.time() - start_time
            
            # Validações
            assert result.complexity_level == case["expected_complexity"], \
                f"Expected {case['expected_complexity']}, got {result.complexity_level}"
            
            logger.info(f"✅ PASS: Strategy={result.strategy_used}, "
                       f"Complexity={result.complexity_level}, "
                       f"Confidence={result.confidence:.2f}, "
                       f"Time={duration:.3f}s, "
                       f"LLM_calls={result.llm_calls_made}")
            
            # Performance checks
            if result.strategy_used == DetectionStrategy.HARDCODED:
                assert duration < 0.2, f"Hardcoded too slow: {duration}s"
            elif result.strategy_used == DetectionStrategy.HYBRID:
                assert duration < 2.0, f"Hybrid too slow: {duration}s"
            else:  # FULL_LLM
                assert duration < 5.0, f"LLM too slow: {duration}s"
                
        except Exception as e:
            logger.error(f"❌ FAIL: {e}")
            raise
    
    logger.info("✅ All Railway compatibility tests passed!")


def test_conversation_context():
    """Testa análise de contexto conversacional."""
    logger.info("\n🧠 Testing conversational context analysis...")
    
    detector = IntelligentContextDetector()
    
    # Simula conversa que causou problema "2023"
    conversation_history = [
        {
            "question": "quanto vendemos em 2024",
            "response": "Em 2024, vendemos um total de R$ 4.473.860.799,93 em operações de câmbio."
        }
    ]
    
    # Query problemática que causou contradição
    query = "e em 2023"
    
    logger.info(f"Context: Previous response about 2024 sales")
    logger.info(f"Query: '{query}'")
    
    result = detector.detect_context(query, conversation_history)
    
    # Validações específicas
    assert result.complexity_level == ContextComplexity.MODERATE, \
        f"Expected MODERATE for reference query, got {result.complexity_level}"
    
    assert result.strategy_used in [DetectionStrategy.HYBRID, DetectionStrategy.FULL_LLM], \
        f"Expected hybrid/LLM for reference query, got {result.strategy_used}"
    
    # Check se detectou referência temporal
    has_temporal = "temporal" in result.entities and len(result.entities["temporal"]) > 0
    has_reference = "reference" in result.entities and len(result.entities["reference"]) > 0
    
    logger.info(f"✅ Conversational analysis: "
               f"Temporal_entities={has_temporal}, "
               f"Reference_entities={has_reference}, "
               f"Strategy={result.strategy_used}")
    
    return result


def test_performance_targets():
    """Testa targets de performance."""
    logger.info("\n⏱️ Testing performance targets...")
    
    detector = IntelligentContextDetector()
    
    performance_tests = [
        {
            "query": "total vendas hoje",
            "target_ms": 100,
            "description": "Simple hardcoded"
        },
        {
            "query": "comparar com mês passado",
            "target_ms": 1500,
            "description": "Hybrid analysis"
        },
        {
            "query": "explique as diferenças entre trimestres e projete tendências",
            "target_ms": 3000,
            "description": "Complex LLM analysis"
        }
    ]
    
    for test in performance_tests:
        logger.info(f"\n📊 Performance test: {test['description']}")
        
        # Múltiplas execuções para média
        times = []
        for _ in range(3):
            start = time.time()
            result = detector.detect_context(test["query"])
            duration = (time.time() - start) * 1000  # ms
            times.append(duration)
        
        avg_time = sum(times) / len(times)
        
        logger.info(f"Target: {test['target_ms']}ms, "
                   f"Actual: {avg_time:.1f}ms, "
                   f"Strategy: {result.strategy_used}")
        
        # Soft target (warning only)
        if avg_time > test['target_ms']:
            logger.warning(f"⚠️ Performance target missed by {avg_time - test['target_ms']:.1f}ms")
        else:
            logger.info(f"✅ Performance target met")


def test_cache_effectiveness():
    """Testa efetividade do cache."""
    logger.info("\n💾 Testing cache effectiveness...")
    
    detector = IntelligentContextDetector()
    
    query = "análise complexa de vendas com múltiplos períodos e comparações"
    
    # First call (cache miss)
    start = time.time()
    result1 = detector.detect_context(query)
    time1 = time.time() - start
    
    # Second call (cache hit)
    start = time.time()
    result2 = detector.detect_context(query)
    time2 = time.time() - start
    
    # Validações
    assert result2.cache_hit == True, "Second call should be cache hit"
    assert time2 < time1 * 0.1, f"Cache should be 10x faster: {time1:.3f}s vs {time2:.3f}s"
    
    logger.info(f"✅ Cache effective: "
               f"First={time1:.3f}s, "
               f"Cached={time2:.3f}s, "
               f"Speedup={time1/time2:.1f}x")


def test_sync_llm_calls():
    """Testa se LLM calls são realmente síncronos."""
    logger.info("\n🔧 Testing sync LLM calls...")
    
    detector = IntelligentContextDetector()
    
    # Query que vai usar LLM
    query = "porque as vendas caíram e como isso se relaciona com a situação anterior"
    
    result = detector.detect_context(query)
    
    # Se chegou aqui sem exception asyncio, LLM calls são sync
    assert result.strategy_used == DetectionStrategy.FULL_LLM, \
        "Complex query should use LLM strategy"
    
    assert result.llm_calls_made > 0, \
        "LLM strategy should make LLM calls"
    
    logger.info(f"✅ Sync LLM calls working: "
               f"{result.llm_calls_made} calls made, "
               f"time={result.processing_time:.3f}s")


def main():
    """Executa todos os testes."""
    logger.info("🚀 Starting IntelligentContextDetector tests...")
    
    try:
        # Core compatibility test
        test_railway_compatibility()
        
        # Specific issue test
        test_conversation_context()
        
        # Performance validation
        test_performance_targets()
        
        # Cache validation
        test_cache_effectiveness()
        
        # Sync LLM validation  
        test_sync_llm_calls()
        
        logger.info("\n🎉 ALL TESTS PASSED! System is Railway-ready.")
        
        # Stats summary
        detector = IntelligentContextDetector()
        stats = detector.get_stats()
        logger.info(f"\nSystem stats: {stats}")
        
    except Exception as e:
        logger.error(f"\n❌ TEST FAILED: {e}")
        raise


if __name__ == "__main__":
    main()