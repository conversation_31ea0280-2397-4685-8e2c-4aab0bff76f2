"""
Context Preservation Engine for DataHero4
=========================================

Sistema principal de preservação de contexto conversacional.
Integra extração de entidades, rastreamento de contexto e resolução de referências.

Key Features:
- Coordenação entre extrator e tracker
- Preservação automática de contexto
- Resolução inteligente de referências
- Integração com LangGraph state
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from .conversational_entity_extractor import ConversationalEntityExtractor, ExtractedEntity
from .conversation_context_tracker import ConversationContextTracker, ConversationContext
from .conversation_memory_store import conversation_memory_store
from .compressive_context_manager import compressive_context_manager
from .composite_context_scorer import composite_context_scorer
from .segment_level_context_manager import segment_level_context_manager
from .temporal_intent_classifier import TemporalIntentClassifier
from .intelligent_context_detector import IntelligentContextDetector, ContextAnalysisResult
from ..utils.context_debug_logger import context_debug_logger

logger = logging.getLogger(__name__)


@dataclass
class ContextPreservationResult:
    """Resultado da preservação de contexto."""
    entities_extracted: Dict[str, List[ExtractedEntity]]
    inherited_context: Dict[str, Any]
    resolved_references: Dict[str, Any]
    context_applied: bool
    confidence_score: float
    processing_time: float
    optimization_metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte resultado para dicionário."""
        return {
            "entities_extracted": {
                k: [e.to_dict() for e in v] 
                for k, v in self.entities_extracted.items()
            },
            "inherited_context": self.inherited_context,
            "resolved_references": self.resolved_references,
            "context_applied": self.context_applied,
            "confidence_score": self.confidence_score,
            "processing_time": self.processing_time
        }


class ContextPreservationEngine:
    """
    Sistema principal de preservação de contexto conversacional.
    
    Coordena:
    - Extração de entidades da query atual
    - Rastreamento de contexto conversacional
    - Resolução de referências pronominais
    - Herança de filtros e condições
    """
    
    def __init__(self):
        """Inicializa o engine de preservação de contexto."""
        self.entity_extractor = ConversationalEntityExtractor()
        self.temporal_intent_classifier = TemporalIntentClassifier()
        self.intelligent_detector = IntelligentContextDetector()  # NEW: Intelligent context detection
        self.context_trackers: Dict[str, ConversationContextTracker] = {}
        self.enable_persistence = True  # CRITICAL FIX: Ativar persistência para preservar contexto entre perguntas
        self.enable_compression = True  # Flag para ativar compressão otimizada
        self.enable_composite_scoring = True  # Flag para ativar scoring composto
        self.enable_segment_level = True  # Flag para ativar segment-level context
        self.enable_intelligent_detection = True  # NEW: Flag para ativar detecção inteligente
        logger.info("🚀 ContextPreservationEngine initialized with intelligent context detection ENABLED")
    
    async def process_query(
        self,
        thread_id: str,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        query_result: Optional[Any] = None
    ) -> ContextPreservationResult:
        """
        Processa uma query e preserva contexto conversacional.
        
        Args:
            thread_id: ID da thread de conversa
            query: Query atual do usuário
            conversation_history: Histórico da conversa
            query_result: Resultado da query anterior (opcional)
            
        Returns:
            ContextPreservationResult: Resultado da preservação
        """
        start_time = datetime.utcnow()
        
        # Obter ou criar tracker para a thread
        tracker = self._get_or_create_tracker(thread_id)
        
        # Analisar intenção temporal da query (ASYNC)
        temporal_intent = await self.temporal_intent_classifier.analyze_temporal_intent(query)

        # Extrair entidades da query atual
        entities = self.entity_extractor.extract_entities(query, conversation_history)

        # Filtrar entidades temporais se não há intenção temporal explícita
        if not temporal_intent.has_temporal_intent:
            entities = self._filter_temporal_entities(entities, temporal_intent, None)  # No intelligent result in legacy method
            logger.info(f"🧠 Filtered temporal entities - no explicit temporal intent detected (confidence: {temporal_intent.confidence:.2f})")

        # Resolver referências antes de atualizar contexto
        resolved_references = self._resolve_references(tracker, entities, query)
        
        # Atualizar contexto com novas entidades
        tracker.update_context(entities, query, query_result)

        # Persistir contexto se habilitado (de forma assíncrona em background)
        if self.enable_persistence:
            self._schedule_context_persistence(thread_id, tracker.context)

        # Obter contexto herdado para próxima query
        inherited_context = tracker.get_inherited_context()
        
        # Calcular score de confiança
        confidence_score = self._calculate_confidence_score(entities, inherited_context)
        
        # Verificar se contexto foi aplicado
        context_applied = self._has_applicable_context(inherited_context)
        
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        result = ContextPreservationResult(
            entities_extracted=entities,
            inherited_context=inherited_context,
            resolved_references=resolved_references,
            context_applied=context_applied,
            confidence_score=confidence_score,
            processing_time=processing_time
        )
        
        logger.info(f"🚀 Processed query for thread {thread_id}: {len(entities)} entity types, confidence: {confidence_score:.2f}")

        # Log para debugging
        context_debug_logger.log_context_processing(
            thread_id=thread_id,
            query=query,
            entities_extracted=entities,
            inherited_context=inherited_context,
            resolved_references=resolved_references,
            confidence_score=confidence_score,
            processing_time=processing_time,
            context_applied=context_applied
        )

        return result

    async def process_query_optimized(
        self,
        thread_id: str,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        query_result: Optional[Dict[str, Any]] = None
    ) -> ContextPreservationResult:
        """
        Versão otimizada do processamento de query usando técnicas de pesquisa.

        Implementa:
        - Compressive Memory (COMEDY framework)
        - Composite Scoring (Generative Agents)
        - Segment-level context

        Args:
            thread_id: ID da thread
            query: Query do usuário
            conversation_history: Histórico da conversa (opcional)
            query_result: Resultado da query (opcional)

        Returns:
            ContextPreservationResult: Resultado otimizado
        """
        start_time = datetime.utcnow()

        try:
            # 1. Segment-Level Context: Segmentar conversa se habilitado
            relevant_segments = []
            if self.enable_segment_level and conversation_history:
                segments = segment_level_context_manager.segment_conversation(
                    thread_id, conversation_history
                )
                relevant_segments = segment_level_context_manager.get_relevant_segments(
                    thread_id, query, max_segments=3
                )
                logger.debug(f"🧩 Found {len(segments)} segments, {len(relevant_segments)} relevant")

            # 2. Compressive Memory: Comprimir histórico se disponível
            compressed_context = None
            if self.enable_compression and conversation_history:
                compressed_context = compressive_context_manager.compress_conversation(
                    conversation_history, thread_id
                )

                # Aplicar contexto comprimido à query
                enriched_query = compressive_context_manager.apply_context_to_query(
                    query, compressed_context
                )
                logger.debug(f"🗜️ Query enriched: '{query}' → '{enriched_query}'")
            else:
                enriched_query = query

            # 2. Extração de entidades inteligente (novo sistema)
            intelligent_result = None
            if self.enable_intelligent_detection:
                intelligent_result = self.intelligent_detector.detect_context(enriched_query, conversation_history)
                entities = intelligent_result.entities
                
                # Log da análise inteligente
                logger.info(f"🧠 Intelligent analysis: {intelligent_result.strategy_used} "
                           f"(complexity: {intelligent_result.complexity_level}, "
                           f"confidence: {intelligent_result.confidence:.2f}, "
                           f"time: {intelligent_result.processing_time:.3f}s)")
                
                # 2.1. Aplicar filtro temporal inteligente
                # Para análise LLM, usar temporal intent analysis apenas como backup
                temporal_intent = await self.temporal_intent_classifier.analyze_temporal_intent(enriched_query)
                entities = self._filter_temporal_entities(entities, temporal_intent, intelligent_result)
            else:
                # Fallback para extração hardcoded com análise temporal clássica
                entities = self.entity_extractor.extract_entities(enriched_query, conversation_history)
                temporal_intent = await self.temporal_intent_classifier.analyze_temporal_intent(enriched_query)
                
                if not temporal_intent.has_temporal_intent:
                    entities = self._filter_temporal_entities(entities, temporal_intent, None)
                    logger.info(f"🧠 Applied temporal filtering - no explicit intent (confidence: {temporal_intent.confidence:.2f})")

            # 3. Obter ou criar tracker para a thread
            tracker = self._get_or_create_tracker(thread_id)

            # 4. Composite Scoring: Rankear contexto existente
            if self.enable_composite_scoring and tracker.context.active_filters:
                current_time = datetime.utcnow()

                # Converter entradas de contexto para formato compatível
                context_entries = []
                for key, entry in tracker.context.active_filters.items():
                    context_entry = {
                        'key': key,
                        'value': entry.value,
                        'entity_type': entry.entity_type.value,
                        'timestamp': entry.created_at.isoformat(),
                        'usage_count': entry.usage_count,
                        'confidence': entry.confidence,
                        'priority': entry.priority.value
                    }
                    context_entries.append(context_entry)

                # Rankear por score composto
                query_context = {'question': enriched_query, 'entities_extracted': entities}
                ranked_entries = composite_context_scorer.rank_context_entries(
                    context_entries, current_time, query_context, top_k=10
                )

                logger.debug(f"🎯 Ranked {len(ranked_entries)} context entries by composite score")

            # 5. Atualizar contexto com novas entidades
            tracker.update_context(entities, enriched_query, query_result)

            # 6. Obter contexto herdado otimizado
            inherited_context = tracker.get_inherited_context()

            # 7. Resolver referências usando contexto comprimido se disponível
            resolved_references = {}
            if compressed_context and compressed_context.reference_info:
                resolved_references = self._resolve_compressed_references(
                    compressed_context.reference_info, entities
                )
            else:
                resolved_references = self._resolve_references(tracker, entities, enriched_query)

            # 8. Calcular score de confiança otimizado
            confidence_score = self._calculate_optimized_confidence(
                entities, inherited_context, compressed_context, resolved_references
            )

            # 9. Verificar se contexto foi aplicado
            context_applied = self._has_applicable_context(inherited_context)

            processing_time = (datetime.utcnow() - start_time).total_seconds()

            # 10. Criar resultado otimizado
            result = ContextPreservationResult(
                entities_extracted=entities,
                inherited_context=inherited_context,
                resolved_references=resolved_references,
                confidence_score=confidence_score,
                context_applied=context_applied,
                processing_time=processing_time
            )

            # 11. Adicionar metadados de otimização
            result.optimization_metadata = {
                "compression_used": compressed_context is not None,
                "compression_ratio": compressed_context.compression_ratio if compressed_context else 1.0,
                "composite_scoring_used": self.enable_composite_scoring,
                "segment_level_used": self.enable_segment_level,
                "segments_found": len(relevant_segments),
                "enriched_query": enriched_query,
                "original_query": query
            }

            compression_ratio = compressed_context.compression_ratio if compressed_context else 1.0
            logger.info(f"🚀 Optimized query processing for thread {thread_id}: "
                       f"{len(entities)} entity types, confidence: {confidence_score:.2f}, "
                       f"compression: {compression_ratio:.2f}")

            # 12. Log para debugging
            context_debug_logger.log_context_processing(
                thread_id=thread_id,
                query=enriched_query,
                entities_extracted=entities,
                inherited_context=inherited_context,
                resolved_references=resolved_references,
                confidence_score=confidence_score,
                processing_time=processing_time,
                context_applied=context_applied
            )

            return result

        except Exception as e:
            logger.error(f"❌ Error in optimized query processing for thread {thread_id}: {e}")
            # Fallback para método original
            return self.process_query(thread_id, query, conversation_history, query_result)
    
    def get_context_for_query_generation(
        self,
        thread_id: str,
        current_query: str
    ) -> Dict[str, Any]:
        """
        Obtém contexto específico para geração de query SQL.
        
        Args:
            thread_id: ID da thread
            current_query: Query atual
            
        Returns:
            Dict com contexto formatado para query generation
        """
        tracker = self._get_or_create_tracker(thread_id)
        inherited_context = tracker.get_inherited_context()
        
        # Formatar contexto para query generation
        query_context = {
            "temporal_conditions": self._format_temporal_conditions(inherited_context.get("temporal_filters", {})),
            "business_filters": self._format_business_filters(inherited_context.get("business_filters", {})),
            "active_conditions": inherited_context.get("active_conditions", {}),
            "resolved_entities": self._resolve_current_entities(tracker, current_query)
        }
        
        return query_context
    
    def _get_or_create_tracker(self, thread_id: str) -> ConversationContextTracker:
        """Obtém ou cria tracker para thread."""
        if thread_id not in self.context_trackers:
            self.context_trackers[thread_id] = ConversationContextTracker(thread_id)
            logger.debug(f"🚀 Created new context tracker for thread {thread_id}")
        
        return self.context_trackers[thread_id]
    
    def _resolve_references(
        self,
        tracker: ConversationContextTracker,
        entities: Dict[str, List[ExtractedEntity]],
        query: str
    ) -> Dict[str, Any]:
        """Resolve referências pronominais na query."""
        resolved = {}
        
        reference_entities = entities.get("reference", [])
        if not reference_entities:
            return resolved
        
        for ref_entity in reference_entities:
            reference_text = ref_entity.value
            resolved_value = tracker.resolve_reference(reference_text)
            
            if resolved_value:
                resolved[reference_text] = {
                    "original": reference_text,
                    "resolved": resolved_value,
                    "confidence": ref_entity.confidence,
                    "position": ref_entity.position
                }
                
                logger.debug(f"🚀 Resolved reference '{reference_text}' to {resolved_value}")
        
        return resolved

    def _filter_temporal_entities(
        self,
        entities: Dict[str, List[ExtractedEntity]],
        temporal_intent: Any,
        intelligent_result: Optional[ContextAnalysisResult] = None
    ) -> Dict[str, List[ExtractedEntity]]:
        """
        Filtra entidades temporais usando análise inteligente.

        Args:
            entities: Entidades extraídas
            temporal_intent: Resultado da análise de intenção temporal (legacy)
            intelligent_result: Resultado da análise inteligente (novo)

        Returns:
            Entidades filtradas
        """
        filtered_entities = entities.copy()

        # NEW: Se temos resultado da análise inteligente, usar sua lógica
        if intelligent_result and self.enable_intelligent_detection:
            # Análise inteligente já considerou contexto conversacional
            # Não fazer filtro agressivo se foi detectado por LLM
            if intelligent_result.strategy_used in ["hybrid", "full_llm"]:
                # LLM analysis já validou as entidades temporais
                logger.info(f"🧠 Preserving temporal entities from {intelligent_result.strategy_used} analysis")
                return filtered_entities
            
            # Para detecção hardcoded, aplicar filtro mais conservador
            if "temporal" in filtered_entities:
                # Threshold mais baixo para entidades hardcoded validadas
                preserved_temporal = [
                    entity for entity in filtered_entities["temporal"]
                    if entity.confidence > 0.6  # Reduzido de 0.8 para 0.6
                ]
                
                if preserved_temporal:
                    filtered_entities["temporal"] = preserved_temporal
                    logger.debug(f"🧠 Kept {len(preserved_temporal)} temporal entities (conservative filter)")
                else:
                    del filtered_entities["temporal"]
                    logger.info("🧠 Removed temporal entities - low confidence hardcoded detection")
            
            return filtered_entities

        # LEGACY: Fallback para análise temporal antiga
        if not temporal_intent.has_temporal_intent and temporal_intent.temporal_type == "none":
            if "temporal" in filtered_entities:
                high_confidence_temporal = [
                    entity for entity in filtered_entities["temporal"]
                    if entity.confidence > 0.8
                ]

                if high_confidence_temporal:
                    filtered_entities["temporal"] = high_confidence_temporal
                    logger.debug(f"🧠 Kept {len(high_confidence_temporal)} high-confidence temporal entities (legacy)")
                else:
                    del filtered_entities["temporal"]
                    logger.info("🧠 Removed all temporal entities - no explicit temporal intent (legacy)")

        return filtered_entities

    def _calculate_confidence_score(
        self,
        entities: Dict[str, List[ExtractedEntity]],
        inherited_context: Dict[str, Any]
    ) -> float:
        """Calcula score de confiança da preservação de contexto."""
        total_score = 0.0
        total_weight = 0.0
        
        # Score baseado em entidades extraídas
        for entity_type, entity_list in entities.items():
            if entity_list:
                type_weight = {
                    "temporal": 0.4,
                    "business": 0.3,
                    "filter": 0.2,
                    "reference": 0.1
                }.get(entity_type, 0.1)
                
                avg_confidence = sum(e.confidence for e in entity_list) / len(entity_list)
                total_score += avg_confidence * type_weight
                total_weight += type_weight
        
        # Score baseado em contexto herdado
        context_weight = 0.3
        if inherited_context:
            context_items = (
                len(inherited_context.get("temporal_filters", {})) +
                len(inherited_context.get("business_filters", {})) +
                len(inherited_context.get("active_conditions", {}))
            )
            
            if context_items > 0:
                context_score = min(context_items / 5.0, 1.0)  # Normalizar para max 5 itens
                total_score += context_score * context_weight
                total_weight += context_weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _has_applicable_context(self, inherited_context: Dict[str, Any]) -> bool:
        """Verifica se há contexto aplicável."""
        return any([
            inherited_context.get("temporal_filters"),
            inherited_context.get("business_filters"),
            inherited_context.get("active_conditions")
        ])
    
    def _format_temporal_conditions(self, temporal_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Formata condições temporais para SQL generation."""
        conditions = {}
        
        for key, filter_data in temporal_filters.items():
            value = filter_data["value"]
            
            if "month_year" in key:
                conditions["date_filter"] = {
                    "type": "month_year",
                    "month": value["month"],
                    "year": value["year"],
                    "sql_condition": f"DATE_PART('year', data_operacao) = {value['year']} AND DATE_PART('month', data_operacao) = {value['month']}"
                }
            elif "year" in key:
                conditions["date_filter"] = {
                    "type": "year",
                    "year": value,
                    "sql_condition": f"DATE_PART('year', data_operacao) = {value}"
                }
        
        return conditions
    
    def _format_business_filters(self, business_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Formata filtros de negócio para SQL generation."""
        filters = {}
        
        for key, filter_data in business_filters.items():
            value = filter_data["value"]
            
            if "currency" in key:
                filters["currency_filter"] = {
                    "currency": value,
                    "sql_condition": self._get_currency_sql_condition(value)
                }
            elif "potential_client" in key:
                filters["client_filter"] = {
                    "client_name": value,
                    "sql_condition": f"LOWER(p.nome) LIKE LOWER('%{value}%')"
                }
        
        return filters
    
    def _get_currency_sql_condition(self, currency: str) -> str:
        """Gera condição SQL para filtro de moeda."""
        if currency == "USD":
            return "m.moeda = 'USD' OR b.id_moeda = 2"
        elif currency == "EUR":
            return "m.moeda = 'EUR'"
        elif currency == "BRL":
            return "m.moeda = 'BRL' OR b.id_moeda = 1"
        else:
            return f"m.moeda = '{currency}'"
    
    def _resolve_current_entities(
        self,
        tracker: ConversationContextTracker,
        current_query: str
    ) -> Dict[str, Any]:
        """Resolve entidades da query atual com contexto."""
        # Extrair entidades da query atual
        current_entities = self.entity_extractor.extract_entities(current_query)
        
        resolved = {}
        
        # Resolver entidades temporais
        for entity in current_entities.get("temporal", []):
            if entity.key == "relative_time":
                # Resolver tempo relativo baseado no contexto
                resolved_temporal = self.entity_extractor.resolve_temporal_entity(entity)
                resolved[f"temporal_{entity.key}"] = resolved_temporal
        
        return resolved
    
    def get_thread_context_summary(self, thread_id: str) -> Optional[Dict[str, Any]]:
        """Obtém resumo do contexto de uma thread."""
        if thread_id not in self.context_trackers:
            return None
        
        tracker = self.context_trackers[thread_id]
        return tracker.get_context_summary()
    
    def clear_thread_context(self, thread_id: str) -> bool:
        """Limpa contexto de uma thread."""
        if thread_id in self.context_trackers:
            del self.context_trackers[thread_id]
            logger.info(f"🚀 Cleared context for thread {thread_id}")
            return True
        return False
    
    def cleanup_expired_threads(self, max_age_hours: int = 24) -> int:
        """Remove threads expiradas."""
        current_time = datetime.utcnow()
        expired_threads = []
        
        for thread_id, tracker in self.context_trackers.items():
            age_hours = (current_time - tracker.context.created_at).total_seconds() / 3600
            if age_hours > max_age_hours:
                expired_threads.append(thread_id)
        
        for thread_id in expired_threads:
            del self.context_trackers[thread_id]
        
        if expired_threads:
            logger.info(f"🚀 Cleaned up {len(expired_threads)} expired thread contexts")
        
        return len(expired_threads)

    def _schedule_context_persistence(self, thread_id: str, context) -> None:
        """Agenda persistência de contexto em background."""
        try:
            import asyncio
            import threading

            def persist_in_background():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        conversation_memory_store.store_context(thread_id, context)
                    )
                    loop.close()
                except Exception as e:
                    logger.error(f"❌ Background persistence failed for {thread_id}: {e}")

            # Executar em thread separada para não bloquear
            thread = threading.Thread(target=persist_in_background, daemon=True)
            thread.start()

        except Exception as e:
            logger.error(f"❌ Failed to schedule persistence for {thread_id}: {e}")

    async def load_persisted_context(self, thread_id: str) -> bool:
        """
        Carrega contexto persistido do banco.

        Args:
            thread_id: ID da thread

        Returns:
            True se contexto foi carregado
        """
        try:
            if not self.enable_persistence:
                return False

            persisted_context = await conversation_memory_store.retrieve_context(thread_id)

            if persisted_context:
                # Criar tracker com contexto carregado
                tracker = ConversationContextTracker(thread_id)
                tracker.context = persisted_context
                self.context_trackers[thread_id] = tracker

                logger.info(f"🚀 Loaded persisted context for thread {thread_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ Failed to load persisted context for {thread_id}: {e}")
            return False

    async def cleanup_persisted_contexts(self, max_age_hours: int = 24) -> int:
        """
        Limpa contextos persistidos expirados.

        Args:
            max_age_hours: Idade máxima em horas

        Returns:
            Número de contextos removidos
        """
        try:
            if not self.enable_persistence:
                return 0

            return await conversation_memory_store.cleanup_expired_contexts(max_age_hours)

        except Exception as e:
            logger.error(f"❌ Failed to cleanup persisted contexts: {e}")
            return 0

    def _resolve_compressed_references(
        self,
        reference_info: str,
        entities: Dict[str, List[ExtractedEntity]]
    ) -> Dict[str, Any]:
        """Resolve referências usando informação comprimida."""
        resolved = {}

        try:
            # Parsear referências do formato comprimido
            # Exemplo: "isso=vendas USD, anterior=maio 2023"
            if not reference_info:
                return resolved

            ref_pairs = reference_info.split(', ')
            for pair in ref_pairs:
                if '=' in pair:
                    ref, resolution = pair.split('=', 1)
                    resolved[ref.strip()] = resolution.strip()

            logger.debug(f"🗜️ Resolved {len(resolved)} references from compressed context")

        except Exception as e:
            logger.warning(f"⚠️ Error resolving compressed references: {e}")

        return resolved

    def _calculate_optimized_confidence(
        self,
        entities: Dict[str, List[ExtractedEntity]],
        inherited_context: Dict[str, Any],
        compressed_context,
        resolved_references: Dict[str, Any]
    ) -> float:
        """Calcula confiança otimizada considerando compressão e scoring."""
        base_confidence = self._calculate_confidence_score(entities, inherited_context)

        # Bonus por compressão bem-sucedida
        compression_bonus = 0.0
        if compressed_context and compressed_context.confidence_score > 0.7:
            compression_bonus = 0.1

        # Bonus por referências resolvidas
        reference_bonus = 0.0
        if resolved_references:
            reference_bonus = min(len(resolved_references) * 0.05, 0.15)

        # Bonus por contexto herdado rico
        context_bonus = 0.0
        total_filters = (
            len(inherited_context.get("temporal_filters", {})) +
            len(inherited_context.get("business_filters", {})) +
            len(inherited_context.get("active_conditions", {}))
        )
        if total_filters > 3:
            context_bonus = 0.1

        optimized_confidence = base_confidence + compression_bonus + reference_bonus + context_bonus

        return min(optimized_confidence, 1.0)

    def get_optimization_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas das otimizações."""
        stats = {
            "compression_enabled": self.enable_compression,
            "composite_scoring_enabled": self.enable_composite_scoring,
            "active_threads": len(self.context_trackers)
        }

        # Estatísticas de compressão
        if self.enable_compression:
            compression_stats = compressive_context_manager.get_cache_stats()
            stats["compression_stats"] = compression_stats

        # Estatísticas de contexto por thread
        thread_stats = []
        for thread_id, tracker in self.context_trackers.items():
            context_summary = tracker.get_context_summary()
            thread_stats.append({
                "thread_id": thread_id,
                "active_filters": context_summary["active_filters_count"],
                "last_update": context_summary["last_update"]
            })

        stats["thread_stats"] = thread_stats

        return stats

    def enable_optimizations(self, compression: bool = True, composite_scoring: bool = True):
        """Ativa/desativa otimizações."""
        self.enable_compression = compression
        self.enable_composite_scoring = composite_scoring

        logger.info(f"🚀 Optimizations updated: compression={compression}, scoring={composite_scoring}")
