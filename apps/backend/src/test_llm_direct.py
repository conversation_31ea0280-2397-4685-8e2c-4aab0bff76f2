#!/usr/bin/env python3
"""Test LLM Provider directly"""

import os
import asyncio

# Set Railway environment
os.environ['FIREWORKS_API_KEY'] = 'fw_3Ze846V6V6iZQuFrQq2Jkk3C'

from tools.llm_provider import LLMProvider

async def main():
    print("🚀 Testing LLM Provider...")
    
    try:
        provider = LLMProvider(
            setor="cambio",
            cliente="L2M", 
            agent_name="query_generator_agent"
        )
        
        print(f"✅ Provider initialized: {provider.provider}")
        print(f"✅ Model: {provider.model}")
        
        # Test simple call
        response = await provider.ainvoke("Generate SQL: SELECT COUNT(*) FROM vendas WHERE ano = 2023")
        print(f"✅ Response: {response[:100]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())