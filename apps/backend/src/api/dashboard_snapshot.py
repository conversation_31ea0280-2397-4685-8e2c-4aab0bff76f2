"""
Dashboard Snapshot API
======================

Endpoint otimizado para servir snapshots de KPIs.
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging
import time

from src.services.snapshot_service import SnapshotService
from src.config.feature_flags import feature_flags
from src.utils.logging_utils import log_api_call, log_health_status

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])


@router.get("/snapshot")
async def get_dashboard_snapshot(
    client_id: str = "L2M",
    regenerate: bool = False
) -> Dict[str, Any]:
    """
    Retorna snapshot mais recente dos KPIs críticos.
    
    Args:
        client_id: Identificador do cliente
        regenerate: Se True, força regeneração do snapshot
        
    Returns:
        Dict com dados do snapshot
    """
    start_time = time.time()

    try:
        # Verificar se feature está habilitada
        if not feature_flags.is_enabled('dashboard_snapshot'):
            raise HTTPException(
                status_code=503,
                detail="Dashboard snapshot feature is disabled"
            )
        
        service = SnapshotService()
        
        # Se solicitado, regenerar snapshot (apenas em dev)
        if regenerate and feature_flags.is_enabled('dev_mode'):
            logger.info("🔄 Regenerando snapshot sob demanda")
            snapshot = service.generate_daily_snapshot(client_id)
        else:
            # Buscar snapshot existente
            snapshot = service.get_latest_snapshot()
            
            # Se não existir, gerar na primeira vez
            if not snapshot:
                logger.info("📸 Gerando snapshot inicial")
                snapshot = service.generate_daily_snapshot(client_id)
        
        # Verificar se houve erro
        if snapshot.get('metadata', {}).get('error'):
            raise HTTPException(
                status_code=500,
                detail=snapshot['metadata'].get('error_message', 'Unknown error')
            )
        
        # Log da requisição
        response_time = (time.time() - start_time) * 1000  # em ms
        log_api_call("/api/dashboard/snapshot", client_id, response_time_ms=response_time, status_code=200)

        # Retornar resposta formatada
        return {
            "success": True,
            "data": snapshot['kpis'],
            "metadata": {
                "generated_at": f"{snapshot['metadata']['date']} {snapshot['metadata']['time']}",
                "client_id": client_id,
                "kpi_count": snapshot['metadata']['kpi_count'],
                "cache_ttl": 3600,  # 1 hora
                "next_update": "03:00 BRT"
            },
            "summary": snapshot['summary']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao buscar snapshot: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard snapshot: {str(e)}"
        )


@router.get("/snapshot/info")
async def get_snapshot_info() -> Dict[str, Any]:
    """Retorna informações sobre o snapshot atual."""
    try:
        service = SnapshotService()
        snapshot = service.get_latest_snapshot()

        if not snapshot:
            return {
                "has_snapshot": False,
                "message": "No snapshot available"
            }

        return {
            "has_snapshot": True,
            "generated_at": f"{snapshot['metadata']['date']} {snapshot['metadata']['time']}",
            "kpi_count": snapshot['metadata'].get('kpi_count', 0),
            "success_rate": snapshot['summary'].get('success_rate', 0),
            "critical_kpis": list(snapshot.get('kpis', {}).keys())
        }

    except Exception as e:
        logger.error(f"Erro ao buscar info do snapshot: {e}")
        return {
            "has_snapshot": False,
            "error": str(e)
        }


@router.get("/snapshot/health")
async def get_snapshot_health() -> Dict[str, Any]:
    """Retorna informações sobre a saúde do sistema de snapshots."""
    try:
        service = SnapshotService()
        health_info = service.get_snapshot_health()

        return {
            "success": True,
            "health": health_info
        }

    except Exception as e:
        logger.error(f"Erro ao verificar saúde do snapshot: {e}")
        return {
            "success": False,
            "health": {
                "status": "error",
                "message": f"Health check failed: {str(e)}"
            }
        }
