# Multi-stage Dockerfile for DataHero4 Backend
# Optimized for Railway deployment

# Stage 1: Build stage
FROM python:3.11-slim AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Configure Poetry - força criação do venv no projeto
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Set working directory
WORKDIR /app

# Copy poetry files first
COPY pyproject.toml poetry.lock ./

# Configure Poetry to create venv in project
RUN poetry config virtualenvs.create true && \
    poetry config virtualenvs.in-project true

# Install dependencies BEFORE copying source code
RUN poetry install --only=main --no-root

# Copy source code after dependencies
COPY src/ ./src/
COPY webapp.py ./

# Install the project itself
RUN poetry install --only-root

# Verify venv was created
RUN ls -la /app/.venv/ && ls -la /app/.venv/bin/

# Clean up
RUN rm -rf $POETRY_CACHE_DIR

# Stage 2: Production stage
FROM python:3.11-slim AS production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -s /bin/bash appuser

# Set working directory
WORKDIR /app

# Copy virtual environment from builder stage
COPY --from=builder --chown=appuser:appuser /app/.venv /app/.venv

# Copy application code
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Add virtual environment to PATH
ENV PATH="/app/.venv/bin:$PATH"

# Expose port
EXPOSE 8000

# Run application
CMD ["uvicorn", "webapp:app", "--host", "0.0.0.0", "--port", "8000"]